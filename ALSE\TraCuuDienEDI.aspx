﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="TraCuuDienEDI.aspx.cs" Inherits="ALSE.TraCuuDienEDI" %>

<asp:Content ID="Content1" ContentPlaceHolderID="HeadContent" runat="server">
    <%# Versioned.VersionedFiles.ScriptHelper.Render("css","css/custom/TraCuuDienEDI.css") %>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="MainContent" runat="server">
    <div class="container-tracuudien">
        <div class="tracuudien-header">
            <h2>TRA CỨU ĐIỆN NCTS</h2>
        </div>
    </div>
    <div class="tracuudien-body">
        <div class="body-input-button">
            <div class="row" id="div-timkiem">
                <div class="col-sm-2">
                </div>
                <div class="col-sm-3">
                    <input type="text" class="form-control input-sm" id="input-mawb" placeholder="Nhập MAWB">
                </div>
                <div class="col-sm-3">
                    <input type="text" class="form-control input-sm" id="input-hawb" placeholder="Nhập HAWB">
                </div>
                <div class="col-sm-1">
                    <input type="button" class="btn btn-sm btn-warning" id="btn-tracuu-ncts" value="Tra cứu">
                </div>
            </div>
            <div class="tracuu-note">
                <span>Lưu ý: Khi nhập số MAWB thì sẽ tra cứu hàng xuất còn nhập số MAWB và HAWB thì sẽ tra cứu hàng nhập</span>
            </div>

        </div>
    </div>
    <%# Versioned.VersionedFiles.ScriptHelper.Render("js","js/custom/TraCuuDienEDI.js") %>
</asp:Content>
