﻿using DocumentFormat.OpenXml.Presentation;
using DocumentFormat.OpenXml.Spreadsheet;
using OfficeOpenXml;
using OfficeOpenXml.FormulaParsing.LexicalAnalysis.TokenSeparatorHandlers;
using OfficeOpenXml.Style;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Net;
using System.Runtime.InteropServices.ComTypes;
using System.Web;
using System.Web.Services;
using System.Web.UI;
using System.Web.UI.WebControls;
using static ALSE.ThanhToan.BaoCao;

namespace ALSE
{
    public partial class BaoCaoOther : System.Web.UI.Page
    {
        private static cs.SIUD siud = new cs.SIUD();
        private static cs.Pr pr = new cs.Pr();
        private static DataSet ds = new DataSet();
        private static string user = "ftp_upload";

        private static string pass = "GY%5kk8*2,q2.T`c";
        private static string ftp = "ftp://*************:1003/OTHER/";


        protected void Page_Load(object sender, EventArgs e)
        {

        }

        #region Load
        [WebMethod]
        public static void DHL_REPORT(cs.HamDungChung.ajaxGet3 ajaxGet3)
        {
            List<DHL> PhiHQTheoMAWB = new List<DHL>();
            List<DHL> BangKeTrucking = new List<DHL>();
            List<DHL> BangKePhiXuLy = new List<DHL>();

            pr.HQGSParameter("1", ajaxGet3.get1, ajaxGet3.get2);
            ds = siud.ScripSIUD(cs.HamDungChung._connectionStringCARGO, "Web_s_baocaoother", "swp", pr.pname, pr.pvalue);
            if (ds.Tables[0].Rows.Count > 0)
            {
                for (int i = 0; i < ds.Tables[0].Rows.Count; i++)
                {
                    PhiHQTheoMAWB.Add(new DHL
                    {
                        SoMaWB = ds.Tables[0].Rows[i]["SoMaWB"].ToString(),
                        SoHAWB = ds.Tables[0].Rows[i]["SoHAWB"].ToString(),
                        ChuyenBay = ds.Tables[0].Rows[i]["ChuyenBay"].ToString(),
                        NgayBayBK = ds.Tables[0].Rows[i]["NgayBayBK"].ToString(),
                        GioBayBK = ds.Tables[0].Rows[i]["GioBayBK"].ToString(),
                        SoKienTHeoHAWB = ds.Tables[0].Rows[i]["SoKienTHeoHAWB"].ToString(),
                        TrongLuong = ds.Tables[0].Rows[i]["TrongLuong"].ToString(),
                        GioLamSLI = ds.Tables[0].Rows[i]["GioLamSLI"].ToString(),
                        MaPV = ds.Tables[0].Rows[i]["MaPV"].ToString(),
                        Warehouse_EXP = ds.Tables[0].Rows[i]["Warehouse_EXP"].ToString(),
                        FWD = ds.Tables[0].Rows[i]["FWD"].ToString(),
                        Factory = ds.Tables[0].Rows[i]["Factory"].ToString(),
                        GhiChu = ds.Tables[0].Rows[i]["GhiChu"].ToString(),
                        NgayLamSLI  = ds.Tables[0].Rows[i]["NgayLamSLI"].ToString(),
                    });
                }

                for (int i = 0; i < ds.Tables[1].Rows.Count; i++)
                {
                    BangKeTrucking.Add(new DHL
                    {
                        SoMaWB = ds.Tables[1].Rows[i]["SoMaWB"].ToString(),
                        SoHAWB = ds.Tables[1].Rows[i]["SoHAWB"].ToString(),
                        ChuyenBay = ds.Tables[1].Rows[i]["ChuyenBay"].ToString(),
                        NgayBayBK = ds.Tables[1].Rows[i]["NgayBayBK"].ToString(),
                        GioBayBK = ds.Tables[1].Rows[i]["GioBayBK"].ToString(),
                        TrongLuong = ds.Tables[1].Rows[i]["TrongLuong"].ToString(),
                        TrongLuongMAWB = ds.Tables[1].Rows[i]["TrongLuongMAWB"].ToString(),
                        GioLamSLI = ds.Tables[1].Rows[i]["GioLamSLI"].ToString(),
                        NgayLamSLI = ds.Tables[1].Rows[i]["NgayLamSLI"].ToString(),
                        MaPV = ds.Tables[1].Rows[i]["MaPV"].ToString(),
                        Warehouse_EXP = ds.Tables[1].Rows[i]["Warehouse_EXP"].ToString(),
                        FWD = ds.Tables[1].Rows[i]["FWD"].ToString(),
                        Factory = ds.Tables[1].Rows[i]["Factory"].ToString(),
                        GhiChu = ds.Tables[1].Rows[i]["GhiChu"].ToString(),
                        TotalHAWB = ds.Tables[1].Rows[i]["TotalHAWB"].ToString(),
                        BKSXeXuat = ds.Tables[1].Rows[i]["BKSXeXuat"].ToString(),
                    });
                }

                for (int i = 0; i < ds.Tables[2].Rows.Count; i++)
                {
                    BangKePhiXuLy.Add(new DHL
                    {
                        SoMaWB = ds.Tables[2].Rows[i]["SoMaWB"].ToString(),
                        SoHAWB = ds.Tables[2].Rows[i]["SoHAWB"].ToString(),
                        TotalHAWB = ds.Tables[2].Rows[i]["TotalHAWB"].ToString(),
                        ChuyenBay = ds.Tables[2].Rows[i]["ChuyenBay"].ToString(),
                        NgayBayBK = ds.Tables[2].Rows[i]["NgayBayBK"].ToString(),
                        SoDNN = ds.Tables[2].Rows[i]["SoDNN"].ToString(),
                        TrongLuong = ds.Tables[2].Rows[i]["TrongLuong"].ToString(),
                        SoKienTrenMAWB = ds.Tables[2].Rows[i]["SoKienTrenMAWB"].ToString(),
                        GioLamSLI = ds.Tables[2].Rows[i]["GioLamSLI"].ToString(),
                        NgayLamSLI = ds.Tables[2].Rows[i]["NgayLamSLI"].ToString(),
                        trong1 = ds.Tables[2].Rows[i]["trong1"].ToString(),
                        trong2 = ds.Tables[2].Rows[i]["trong2"].ToString(),
                        MaPV = ds.Tables[2].Rows[i]["MaPV"].ToString(),
                        Warehouse_EXP = ds.Tables[2].Rows[i]["Warehouse_EXP"].ToString(),
                        FWD = ds.Tables[2].Rows[i]["FWD"].ToString(),
                        GhiChu = ds.Tables[2].Rows[i]["GhiChu"].ToString(),
                    });
                }
            }

            Save_DHL_ReportExcelToFTP(ajaxGet3.get3, PhiHQTheoMAWB, BangKeTrucking, BangKePhiXuLy);
        }

        public static void Save_DHL_ReportExcelToFTP(string filename, List<DHL> PhiHQTheoMAWB, List<DHL> BangKeTrucking, List<DHL> BangKePhiXuLy)
        {
            using (var stream = Create_DHL_ReportExcelFile(PhiHQTheoMAWB, BangKeTrucking, BangKePhiXuLy))
            {
                var buffer = stream as MemoryStream;
                try
                {
                    FtpWebRequest request = (FtpWebRequest)WebRequest.Create(ftp + "BAOCAO" + "/" + filename + ".xlsx");
                    request.Method = WebRequestMethods.Ftp.UploadFile;
                    request.Credentials = new NetworkCredential(user, pass);
                    request.ContentLength = stream.Length;
                    request.UsePassive = true;
                    request.UseBinary = true;
                    request.ServicePoint.ConnectionLimit = (int)stream.Length;
                    request.EnableSsl = false;

                    using (Stream requestStream = request.GetRequestStream())
                    {
                        Byte[] buffer_ = buffer.ToArray();
                        int bytesRead = buffer_.Length;
                        requestStream.Write(buffer_, 0, buffer_.Length);
                        requestStream.Close();
                    }

                    FtpWebResponse response = (FtpWebResponse)request.GetResponse();

                    response.Close();
                }
                catch (WebException ex)
                {
                    throw new Exception((ex.Response as FtpWebResponse).StatusDescription);
                }
            }
        }


        public static Stream Create_DHL_ReportExcelFile(List<DHL> PhiHQTheoMAWB, List<DHL> BangKeTrucking, List<DHL> BangKePhiXuLy)
        {
            WebClient request = new WebClient();
            string url = "";
            byte[] newFileData = null;
            string folder = "BAOCAO/TEMP";
            request.Credentials = new NetworkCredential(cs.ftp._user, cs.ftp._pass);
            url = ftp + folder + "/" + "DHL_TEMP.xlsx";
            try
            {
                newFileData = request.DownloadData(url);
            }
            catch (WebException e)
            {
                // Do something such as log error, but this is based on OP's original code
                // so for now we do nothing.
            }
            using (var excelPackage = new ExcelPackage(new MemoryStream(newFileData)))
            {
                excelPackage.Workbook.Properties.Author = "KENJI";
                excelPackage.Workbook.Properties.Title = "";
                excelPackage.Workbook.Properties.Comments = "";
                var workSheet1 = excelPackage.Workbook.Worksheets[1];
                var workSheet2 = excelPackage.Workbook.Worksheets[2];
                var workSheet3 = excelPackage.Workbook.Worksheets[3];
                //workSheet1.Cells[18, 2].LoadFromCollection(PhiHQTheoMAWB, false);
                //workSheet2.Cells[19, 2].LoadFromCollection(BangKeTrucking, false);
                //workSheet3.Cells[18, 2].LoadFromCollection(BangKePhiXuLy, false);

                int currentRow = 18;

                foreach (var item in PhiHQTheoMAWB)
                {
                    workSheet1.Cells["B" + currentRow.ToString()].Value = item.SoMaWB;
                    workSheet1.Cells["C" + currentRow.ToString()].Value = item.SoHAWB;
                    workSheet1.Cells["D" + currentRow.ToString()].Value = item.ChuyenBay;
                    workSheet1.Cells["E" + currentRow.ToString()].Value = item.NgayBayBK;
                    workSheet1.Cells["F" + currentRow.ToString()].Value = item.GioBayBK;
                    workSheet1.Cells["G" + currentRow.ToString()].Value = item.SoKienTHeoHAWB;
                    workSheet1.Cells["H" + currentRow.ToString()].Value = item.TrongLuong;
                    workSheet1.Cells["I" + currentRow.ToString()].Value = item.GioLamSLI;
                    workSheet1.Cells["J" + currentRow.ToString()].Value = item.NgayLamSLI;
                    workSheet1.Cells["L" + currentRow.ToString()].Value = "";
                    workSheet1.Cells["M" + currentRow.ToString()].Value = item.Warehouse_EXP;
                    workSheet1.Cells["N" + currentRow.ToString()].Value = item.FWD;
                    workSheet1.Cells["O" + currentRow.ToString()].Value = item.Factory;
                    workSheet1.Cells["P" + currentRow.ToString()].Value = item.GhiChu;
                    workSheet1.Cells["Q" + currentRow.ToString()].Value = "";
                    currentRow++;
                }

                int currentRow2 = 19;

                foreach (var item in BangKeTrucking)
                {
                    workSheet2.Cells["B" + currentRow2.ToString()].Value = item.SoMaWB;
                    workSheet2.Cells["C" + currentRow2.ToString()].Value = item.SoHAWB;
                    workSheet2.Cells["D" + currentRow2.ToString()].Value = item.ChuyenBay;
                    workSheet2.Cells["E" + currentRow2.ToString()].Value = item.NgayBayBK;
                    workSheet2.Cells["F" + currentRow2.ToString()].Value = item.GioBayBK;
                    workSheet2.Cells["G" + currentRow2.ToString()].Value = item.TrongLuong;
                    workSheet2.Cells["H" + currentRow2.ToString()].Value = item.TotalHAWB;
                    workSheet2.Cells["I" + currentRow2.ToString()].Value = item.TrongLuongMAWB;
                    workSheet2.Cells["J" + currentRow2.ToString()].Value = item.GioLamSLI;
                    workSheet2.Cells["L" + currentRow2.ToString()].Value = item.NgayLamSLI;
                    workSheet2.Cells["M" + currentRow2.ToString()].Value = "";
                    workSheet2.Cells["N" + currentRow2.ToString()].Value = item.Warehouse_EXP;
                    workSheet2.Cells["O" + currentRow2.ToString()].Value = item.FWD;
                    workSheet2.Cells["P" + currentRow2.ToString()].Value = item.Factory;
                    workSheet2.Cells["Q" + currentRow2.ToString()].Value = item.GhiChu;
                    workSheet2.Cells["R" + currentRow2.ToString()].Value = item.BKSXeXuat;
                    currentRow2++;
                }

                int currentRow3 = 18;

                foreach (var item in BangKeTrucking)
                {
                    workSheet3.Cells["B" + currentRow3.ToString()].Value = item.SoMaWB;
                    workSheet3.Cells["C" + currentRow3.ToString()].Value = item.SoHAWB;
                    workSheet3.Cells["D" + currentRow3.ToString()].Value = item.TotalHAWB;
                    workSheet3.Cells["E" + currentRow3.ToString()].Value = item.ChuyenBay;
                    workSheet3.Cells["F" + currentRow3.ToString()].Value = item.NgayBayBK;
                    workSheet3.Cells["G" + currentRow3.ToString()].Value = item.SoDNN;
                    workSheet3.Cells["I" + currentRow3.ToString()].Value = item.TrongLuong;
                    workSheet3.Cells["J" + currentRow3.ToString()].Value = item.SoKienTrenMAWB;
                    workSheet3.Cells["K" + currentRow3.ToString()].Value = item.TotalHAWB;
                    workSheet3.Cells["P" + currentRow3.ToString()].Value = item.GioLamSLI;
                    workSheet3.Cells["Q" + currentRow3.ToString()].Value = item.NgayLamSLI;
                    workSheet3.Cells["R" + currentRow3.ToString()].Value = "";
                    workSheet3.Cells["S" + currentRow3.ToString()].Value = "";
                    workSheet3.Cells["U" + currentRow3.ToString()].Value = "";
                    workSheet3.Cells["V" + currentRow3.ToString()].Value = item.Warehouse_EXP;
                    workSheet3.Cells["W" + currentRow3.ToString()].Value = item.Factory;
                    workSheet3.Cells["X" + currentRow3.ToString()].Value = item.GhiChu;
                    currentRow3++;
                }

                excelPackage.Save();
                return excelPackage.Stream;
                //return new MemoryStream(newFileData);
            }
        }



        [WebMethod]
        public static void JUSDA_REPORT(cs.HamDungChung.ajaxGet3 ajaxGet3)
        {
            List<JUSDA> list = new List<JUSDA>();
            pr.HQGSParameter("2", ajaxGet3.get1, ajaxGet3.get2);
            ds = siud.ScripSIUD(cs.HamDungChung._connectionStringCARGO, "Web_s_baocaoother", "swp", pr.pname, pr.pvalue);

            if (ds.Tables[0].Rows.Count > 0)
            {
                for (int i = 0; i < ds.Tables[0].Rows.Count; i++)
                {
                    list.Add(new JUSDA
                    {
                        MAWB = ds.Tables[0].Rows[i]["MAWB"].ToString(),
                        HAWB = ds.Tables[0].Rows[i]["HAWB"].ToString(),
                        SoToKhai = ds.Tables[0].Rows[i]["SoToKhai"].ToString(),
                        SoKien = ds.Tables[0].Rows[i]["SoKien"].ToString(),
                        DichVu = ds.Tables[0].Rows[i]["DichVu"].ToString(),
                        GhiChu = ds.Tables[0].Rows[i]["GhiChu"].ToString(),
                        NgayGioContDenNhaMay = returnDateTime(ds.Tables[0].Rows[i]["NgayGioContDenNhaMay"].ToString().Trim(), "dt"),
                        BienKiemSoat = ds.Tables[0].Rows[i]["BienKiemSoat"].ToString(),
                        NhaCungCapVanTai = ds.Tables[0].Rows[i]["NhaCungCapVanTai"].ToString(),
                        Tuyen = ds.Tables[0].Rows[i]["Tuyen"].ToString()
                    });
                }
            }

            Save_Jusda_ReportExcelToFTP(ajaxGet3.get3, list);
        }
        #endregion
        public static void Save_Jusda_ReportExcelToFTP(string filename, List<JUSDA> jUSDAs)
        {
            using (var stream = Create_JUSDA_ReportExcelFile(jUSDAs))
            {
                var buffer = stream as MemoryStream;
                try
                {
                    FtpWebRequest request = (FtpWebRequest)WebRequest.Create(ftp + "BAOCAO" + "/" + filename + ".xlsx");
                    request.Method = WebRequestMethods.Ftp.UploadFile;
                    request.Credentials = new NetworkCredential(user, pass);
                    request.ContentLength = stream.Length;
                    request.UsePassive = true;
                    request.UseBinary = true;
                    request.ServicePoint.ConnectionLimit = (int)stream.Length;
                    request.EnableSsl = false;

                    using (Stream requestStream = request.GetRequestStream())
                    {
                        Byte[] buffer_ = buffer.ToArray();
                        int bytesRead = buffer_.Length;
                        requestStream.Write(buffer_, 0, buffer_.Length);
                        requestStream.Close();
                    }

                    FtpWebResponse response = (FtpWebResponse)request.GetResponse();

                    response.Close();
                }
                catch (WebException ex)
                {
                    throw new Exception((ex.Response as FtpWebResponse).StatusDescription);
                }
            }
        }

        public static Stream Create_JUSDA_ReportExcelFile(List<JUSDA> list)
        {
            WebClient request = new WebClient();
            string url = "";
            byte[] newFileData = null;
            string folder = "BAOCAO/TEMP";
            request.Credentials = new NetworkCredential(cs.ftp._user, cs.ftp._pass);
            url = ftp + folder + "/" + "JUSDA_TEMP.xlsx";
            try
            {
                newFileData = request.DownloadData(url);
            }
            catch (WebException e)
            {
                // Do something such as log error, but this is based on OP's original code
                // so for now we do nothing.
            }
            using (var excelPackage = new ExcelPackage(new MemoryStream(newFileData)))
            {
                excelPackage.Workbook.Properties.Author = "KENJI";
                excelPackage.Workbook.Properties.Title = "";
                excelPackage.Workbook.Properties.Comments = "";
                var workSheet = excelPackage.Workbook.Worksheets[1];
                workSheet.Cells[2, 1].LoadFromCollection(list, false);
                excelPackage.Save();
                return excelPackage.Stream;
                //return new MemoryStream(newFileData);
            }
        }
        #region Excel

        #endregion

        #region Class 
        public class JUSDA
        {
            public string MAWB { get; set; }
            public string HAWB { get; set; }
            public string SoToKhai { get; set; }
            public string SoKien { get; set; }
            public string DichVu { get; set; }
            public string GhiChu { get; set; }
            public DateTime? NgayGioContDenNhaMay { get; set; }
            public string BienKiemSoat { get; set; }
            public string NhaCungCapVanTai { get; set; }
            public string Tuyen { get; set; }
        }

        public class DHL
        {
            public string SoMaWB { get; set; }
            public string SoHAWB { get; set; }
            public string ChuyenBay { get; set; }
            public string NgayBayBK { get; set; }
            public string GioBayBK { get; set; }
            public string SoKienTHeoHAWB { get; set; }
            public string TotalHAWB { get; set; }
            public string TrongLuongMAWB { get; set; }
            public string GioLamSLI { get; set; }
            public string NgayLamSLI { get; set; }
            public string MaPV { get; set; }
            public string Warehouse_EXP { get; set; }
            public string FWD { get; set; }
            public string Factory { get; set; }
            public string GhiChu { get; set; }

            public string SoDNN { get; set; }
            public string TrongLuong { get; set; }
            public string SoKienTrenMAWB { get; set; }
            public string trong1 { get; set; }
            public string trong2 { get; set; }
            public string BKSXeXuat { get; set; }


        }
        #endregion

        #region OtherFunction

        /// <summary>
        ///
        /// </summary>
        /// <param name="dt"></param>
        /// <returns></returns>
        public static DateTime? returnDateTime(string dt, string type)
        {
            DateTime? re_dt = null;
            if (dt != "" && dt != "-")
            {
                string[] arr_date = dt.Split(' ')[0].Split('/');
                string[] arr_time = dt.Split(' ')[1].Split(':');
                int timeAMPM = 0;

                if (type == "dt")
                {
                    if (dt.Split(' ').Length < 3)
                    {
                        re_dt = new DateTime(int.Parse(arr_date[2])
                   , int.Parse(arr_date[0])
                   , int.Parse(arr_date[1])
                   , int.Parse(arr_time[0])
                   , int.Parse(arr_time[1])
                   , int.Parse(arr_time[2])

                   );
                    }
                    else
                    {
                        timeAMPM = int.Parse(arr_time[0]);
                        if (dt.Split(' ')[2] == "PM" && int.Parse(arr_time[0]) < 12)
                        {
                            timeAMPM = int.Parse(arr_time[0]) + 12;
                        }
                        re_dt = new DateTime(int.Parse(arr_date[2])
                    , int.Parse(arr_date[0])
                    , int.Parse(arr_date[1])
                    , timeAMPM
                    , int.Parse(arr_time[1])
                    , int.Parse(arr_time[2])

                    );
                    }
                }

                if (type == "d")
                {
                    re_dt = new DateTime(int.Parse(arr_date[2])
                    , int.Parse(arr_date[0])
                    , int.Parse(arr_date[1])
                    );
                }
                if (dt.Split(' ')[0] == "1/1/1900")
                {
                    re_dt = null;
                }
            }

            return re_dt;
        }

        public static TimeSpan? returnTimeSpan(string dt)
        {
            int timeAMPM = 0;

            string[] arr_time = dt.Split(' ')[1].Split(':');
            timeAMPM = int.Parse(arr_time[0]);
            if (dt.Split(' ')[2] == "PM" && int.Parse(arr_time[0]) < 12)
            {
                timeAMPM = int.Parse(arr_time[0]) + 12;
            }
            TimeSpan? ts = new TimeSpan(timeAMPM
                , int.Parse(arr_time[1])
                , int.Parse(arr_time[2])

                );
            if (dt.Split(' ')[0] == "1/1/1900" || dt.Split(' ')[0] == "01/01/1900")
            {
                ts = null;
            }

            return ts;
        }
        #endregion
    }
}