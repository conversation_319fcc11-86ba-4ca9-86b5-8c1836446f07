﻿using DevExpress.ExpressApp.Win.Templates.Controls;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Sockets;
using System.Text.RegularExpressions;
using System.Web;

namespace ALSE
{
    public partial class SiteMaster : System.Web.UI.MasterPage
    {
        private List<string> listboss = new List<string>(new string[] { "1", "8", "9", "12", "20", "25", "130", "30" });
        private List<string> listtruck = new List<string>(new string[] { "1", "8", "20", "94", "130" });
        private List<string> listDistribution = new List<string>(new string[] { "1", "8", "9", "12", "20", "29", "94", "130", "21" });
        private List<string> listtruckGiaMuaBan = new List<string>(new string[] { "1", "8", "20", "94", "130" });
        private List<string> listnvnhap = new List<string>(new string[] { "15", "18", "21", "31" });
        private List<string> listIdAdmin = new List<string>(new string[] { "1", "8", "9" });
        private List<string> listIdHQYP = new List<string>(new string[] { "70", "85", "88", "136", "166" });

        //private List<string> listSupplies = new List<string>(new string[] { "1", "103", "142","116" });
        private string[] path = (HttpContext.Current.Request.Url.AbsolutePath).Split('.');

        protected void Page_Init(object sender, EventArgs e)
        {
            if (Session["WebLoginName"] == null || (string)Session["WebLoginName"] == "")

            {
                Response.Redirect("~/DangNhap.aspx?UrlBack=" + path[0]);
            }
        }

        protected void Page_Load(object sender, EventArgs e)
        {
            Page.Header.DataBind();
            Page.DataBind();
            vCongVanHQ.Visible = false;
            vVattu.Visible = false;
            //if (!IsPostBack)
            //{
            // eusu
            vQuanLyKhoThuong2.Visible = false;
            vExportSafo.Visible = false;

            VKT.Visible = false;
            vExportEFL.Visible = false;
            vExportAPX.Visible = false;
            //end eusu
            //maple
            vLogisticsManager.Visible = false;
            vQuanLyKhoThuong1.Visible = false;
            //Literal1.Text = "<span>" + GetIPAddress()[0] + " " + GetIPAddress()[1] + " " + GetIPAddress()[2] + "</span>";
            Session["ngaytd"] = null;
            string wug = (string)Session["WUGroup"];
            string uid = (string)Session["WebUID"];
            string wphong = (string)Session["WUPhong"];
            string[] url = { "BaoCaoXuatKho", "QuanLyPhieuCan", "TrangThaiHangXuat", "ViTri", "QuanLyChamCong", "DanhSachDuyetIO", "ThemChamCong", "QLBaoCaoKT" };
            string[] urlboss = { "QuanLyChamCong", "DanhSachDuyetIO", "ThemChamCong", };
            string[] urlfwd = { "BaoCaoXuatKho", "QuanLyPhieuCan", "TrangThaiHangXuat", "ViTri", "ChamCong", "BangChamCong", "QuanLyChamCong", "DanhSachDuyetIO", "ThemChamCong", "BCC", "DVAIPS", "ThongBao", "DSLoi", "QLBaoCaoKT", "KiemSoatHSC", "XuatBaoCao", "InputExcel" };
            string[] urlaips = { "ViTri", "ChamCong", "BangChamCong", "QuanLyChamCong", "DanhSachDuyetIO", "ThemChamCong", "BCC", "ThongBao", "DSLoi", "QLBaoCaoKT", "KiemSoatHSC", "XuatBaoCao", "TrangThaiHangXuat", "TrangThaiHangNhap", "BaoCao", "QuanLyKHVC", "QLBaoCaoKT", "KiemSoatHSC", "InputExcel" };
            string[] urlhq = { "Default", "BaoCaoXuatKho", "QuanLyPhieuCan", "TrangThaiHangXuat", "ViTri", "ChamCong", "BangChamCong", "QuanLyChamCong", "DanhSachDuyetIO", "ThemChamCong", "BCC", "DVAIPS", "ThongBao", "DSLoi", "QLBaoCaoKT", "KiemSoatHSC", "XuatBaoCao", "TrangThaiHangNhap", "QuanLyKHVC", "InputExcel" };
            string[] urlpantos = { "BangChamCong"
                                    , "BangTongHopDVASG", "BaoCaoTongHop", "BaoCaoXuatKho"
                                    , "BCC", "BCCwU", "ChinhSuaKHVC"
                                    , "ChiTietDonHang", "ConsolManifest", "DangKyVatTu"
                                    , "BangTongHopDVASG", "BaoCaoTongHop", "BaoCaoXuatKho"
                                    ,  "DNNCHECK", "DoThiNhapHang"
                                    , "DoThiXuatHang", "DVAIPS", "KeHoachLayHang"
                                    , "KiemSoatHSC", "NhapDaiLy", "NhapGTK"
                                    , "NhapTextNBA", "QLBaoCaoKT", "QuanLyDangKyVT"
                                    , "QuanLyPhieuCan", "ReportHaiQuan", "ThongBao"
                                    , "ThongBaoGNLBL", "ThongBaowID", "TrangThaiHangNhap"
                                    , "TrangThaiHangNhapASG", "TrangThaiHangXuat", "TrangThaiHangXuatASG"
                                    , "TruyVanHangXuat", "ViTri", "XuatBaoCao"
                                    };
            string[] urlhqyp = { "Default", "QuanLyLogistics", "DGR", "ChamCongTrangThai", "QuanLyBaoCaoNgay", "CongViecBangTin", "QuanLyLoi" };
            ltridname.Text = (string)Session["WUFullName"];
            ltrUGroup.Text = "userid=" + uid + " wugroup=" + wug + " wugroupname=" + (string)Session["WUGroupName"];
            bool checkurl = url.Contains(path[0].Replace("/", ""));
            bool checkurlboss = urlboss.Contains(path[0].Replace("/", ""));
            bool checkurlfwd = urlfwd.Contains(path[0].Replace("/", ""));
            bool checkurlaips = urlaips.Contains(path[0].Replace("/", ""));
            bool checkurlhq = urlhq.Contains(path[0].Replace("/", ""));
            bool checkurlhqyp = urlhqyp.Contains(path[0].Replace("/", ""));

            vTTHXASG.Visible = false;
            vTTHNASG.Visible = false;
            cTruyVanDNN.Visible = false;
            if (wug != "1") // Khác nhóm ALSE
            {
                vCPN.Visible = false;
                vOPS.Visible = false;
                vThanhToan.Visible = false;
                DGR.Visible = false;
                vChamCong.Visible = false; // Chấm Công
                vXBC.Visible = false; // Xuất Báo Cáo
                vTTHX.Visible = false; // Hàng Xuất
                vDVAIPS.Visible = false;
                vThongBao.Visible = false;
                vDSLoi.Visible = false;
                vQLBC.Visible = false;
                vKSHSC.Visible = false;
                vInputExcel.Visible = false;
                vQuanLyKhoThuong.Visible = false;
                vQuanLyLogistic.Visible = false;
                vDistribution.Visible = false;
                vTruck.Visible = false;
                vTruckTraCuu.Visible = false;
                vTruckGiaBan.Visible = false;
                vTruckGiaMua.Visible = false;
                vTruckKhachHang.Visible = false;
                vCongViec.Visible = false;
                vQuanLyHangNhap.Visible = false;
                vQuanLyHangXuat.Visible = false;
                vDanhSachVatTu.Visible = false;
                vNhapVatTu.Visible = false;
                vDeNghiCapPhat.Visible = false;
                vTraCuuDim.Visible = false;
                vQuanLyKhoHang.Visible = false;
                vTrienKhaiCV.Visible = false;
                if (wug == "8")
                {
                    vTTHN.Visible = false;
                    vMEG.Visible = false;
                }
                if ((checkurl == true) && ((wug != "3") || (wug != "4")))
                {
                    if (wug != "9" && (wug != "28"))
                    {
                        Response.Redirect("~/Default.aspx");
                    }
                }
                else if (((wug == "3") || (wug == "4")) && checkurlfwd == true)
                {
                    Response.Redirect("~/Default.aspx");
                }

                if (wug == "8" && path[0].Replace("/", "") != "ReportHaiQuan" && checkurlhq == true)
                {
                    Response.Redirect("~/ReportHaiQuan.aspx");
                }
                if (wug == "24")
                {
                    vMEG.Visible = false;
                    if (path[0].Replace("/", "") != "TrangThaiHangNhap")
                    {
                        Response.Redirect("~/TrangThaiHangNhap.aspx");
                    }
                }

                vOtherTool.Visible = false;
            }
            else
            {
                vCPN.Visible = true;
                vMEGHangXuatKH.Visible = false;
                vTTHNKhachHang.Visible = false;
                vTTHN.Visible = false;
                vTTHX.Visible = false;
                vMEG.Visible = false;
                vTraCuuDim.Visible = false;
                vOtherTool.Visible = true;
                vCongVanHQ.Visible = true;
                vQuanLyKhoHang.Visible = false;
                ///
                /// Nếu user id khác list dưới đây thì menu vThanhToán bị ẩn đi
                ///
                List<string> listThanhToanAdmin = new List<string>(new string[] { "1",     // mr Admin
                                                                            "8",    // mr Tiến
                                                                            "9",    // mr Khánh
                                                                            "20",   // mr Tú
                                                                            "83",   // ms Giang kế toán
                                                                            "84",   // ms Nga kế toán
                                                                            "103",  // ms Hương kế toán
                                                                            "12" ,  // mr Long
                                                                            "111",    // Ms Vĩnh
                                                                            "122",    // Ms Viên
                                                                            "94", // mr Hiếu
                                                                            //"134", // mr Huy
                                                                            "130", // ms  Xuyến
                                                                            "186",// mr Hiền
                                                                            "195", // Trang 
                                                                            "203", // Trang 2
                                                                            "204", // Dũng PM
                                                                            "209" // Thảo NT
                                                                        });

                if (listThanhToanAdmin.Any(x => x == uid) == false)
                {
                    vThanhToan.Visible = false;
                }
                if (listDistribution.Any(x => x == uid) == false)
                {
                    vDistribution.Visible = false;
                }
                if (listtruck.Any(x => x == uid) == false)
                {
                    vTruck.Visible = false;
                    vTruckTraCuu.Visible = false;
                    vTruckKhachHang.Visible = false;
                    vTruckTuyen.Visible = false;
                    vTruckGiaBan.Visible = false;
                    vTruckGiaMua.Visible = false;
                }
                //if (listtruckGiaMuaBan.Any(x => x == uid) == false)
                //{
                //}
                if (wphong == "TH" && uid != "116" && uid != "20" && uid != "99") // 116 = toannh, 20 = tunv, 99  đã xóa
                {
                    vTTHX.Visible = false;
                    if (uid != "111" && uid != "84" & uid != "83") // 111 = vinhnt, 84 = nghnt, 83 = giangnh
                    {
                        vTTHN.Visible = false;
                    }

                    vQuanLyKhoThuong.Visible = false;
                    vQuanLyLogistic.Visible = false;
                    DGR.Visible = false;
                    vKSHSC.Visible = false;
                    vThongBao.Visible = false;
                    vOtherTool.Visible = false;

                    // new rules update 13/03/2019 16:23
                    if (uid == "122" || uid == "186" || uid == "195" || uid == "203" || uid == "204" || uid == "209") // 122 = vienn , 186 = hienvt , 195 = trangnt , 203 = trang , 204 = dũng , 209 = thảo
                    {
                        // hiển thị export, import, logistics, DGR, Other tools
                        vTTHX.Visible = false;
                        vTTHN.Visible = false;
                        vQuanLyKhoThuong.Visible = true;
                        vQuanLyLogistic.Visible = true;
                        DGR.Visible = true;
                        vOtherTool.Visible = true;
                    }
                }

                // cấu hình đội Yên phong
                if (listIdHQYP.Any(x => x == uid))
                {
                    vQuanLyHangXuat.Visible = false;
                    vQuanLyHangNhap.Visible = false;
                    vQuanLyKhoThuong.Visible = false;
                    vKSHSC.Visible = false;
                    vOtherTool.Visible = false;
                    vThongBao.Visible = false;

                    if (!checkurlhqyp)
                    {
                        Response.Redirect("~/Default.aspx");
                    }

                }

                // end cấu hình đội yên phong
            }


            if(wug == "17" || wug == "33")
            {
                cTruyVanDNN.Visible = true;
            }

            if (wug == "55")// MAXIM
            {
                vMEG.Visible = false;
                vTTHN.Visible = false;
            }


            if (wug == "52")// GTT
            {
                vMEG.Visible = false;
                vMEGHangXuatKH.Visible = false;
                vTTHN.Visible = false;
            }

            //DAM
            if (wug == "7")
            {
                vTTHNKhachHang.Visible = false;
                vMEGHangXuatKH.Visible = false;
                vMEG.Visible = true;
                vTTHN.Visible = false;
            }
            //DAM
            if (wug == "8")
            {
                vTTHNKhachHang.Visible = false;
                vMEGHangXuatKH.Visible = false;
                vMEG.Visible = false;
                vTTHN.Visible = false;
            }

            //OPS
            if (wug == "44")
            {
                vOPS.Visible = true;
                vMEG.Visible = false;
                vTTHN.Visible = false;
            }

            //SALES
            if (wug == "45")
            {
                vOPS.Visible = false;
                vMEG.Visible = false;
                vTTHN.Visible = false;
                vQuanLyLogistic.Visible = true;
            }
            //BOL
            if (wug == "50")
            {
                vTTHNKhachHang.Visible = false;
                vMEGHangXuatKH.Visible = false;
                vMEG.Visible = true;
                vTTHN.Visible = false;
            }


            //BOL
            if (wug == "51")
            {

                vTTHNKhachHang.Visible = true;
                vMEGHangXuatKH.Visible = false;
                vMEG.Visible = false;
                vTTHN.Visible = false;
            }

            //MOR
            if (wug == "53")
            {
                vTTHNKhachHang.Visible = false;
                vMEGHangXuatKH.Visible = true;
                vMEG.Visible = false;
                vTTHN.Visible = false;
            }

            if (wug == "54")
            {
                vTTHNKhachHang.Visible = false;
                vMEGHangXuatKH.Visible = false;
                vMEG.Visible = false;
                vTTHN.Visible = false;
                vQuanLyKhoThuong1.Visible = true;
            }


            //AIPS
            if (wug == "9")
            {
                vThongBao.Visible = false;
                vDSLoi.Visible = false;
                vQLBC.Visible = false;
                vChamCong.Visible = false;
                vTTHN.Visible = false;
                vMEG.Visible = false;
                vTTHXASG.Visible = true;
                vTTHNASG.Visible = true;
                if (checkurlaips == true)
                {
                    Response.Redirect("~/Default.aspx");
                }
            }
            //

            if (listboss.Any(x => x == uid) == false)
            {
                //vChamCong.Visible = false;
                vXBC.Visible = false;

                if (checkurlboss == true)
                {
                    Response.Redirect("~/Default.aspx");
                }
            }
            if (listnvnhap.Any(x => x == uid) == true)
            {
                vXBC.Visible = true;
            }
            if (uid == "122" || uid == "186" || uid == "186" || uid == "196" || uid == "203" || uid == "195" || uid == "204" || uid == "209")
            {
                vXBC.Visible = true;
            }



            if (wug == "10")
            {
                vTTHXASG.Visible = false;
                vTTHNASG.Visible = false;
                vTTHN.Visible = false;
                vMEGHangXuatKH.Visible = true;
                vMEG.Visible = false;
            }
            if (wug == "12")
            {
                VKT.Visible = true;
                vTTHN.Visible = false;
                vMEG.Visible = false;
            }
            //if (listIdAdmin.Any(x => x == uid) == true)
            //{
            //    vOtherTool.Visible = true;
            //}
            //else {
            //    vOtherTool.Visible = false;
            //}
            if (wug == "15" || wug == "19")
            {
                vLogisticsManager.Visible = true;
                vTTHN.Visible = false;
                vMEG.Visible = false;
            }
            if (wug == "13" || wug == "16" || wug == "18" || wug == "20" || wug == "21" || wug == "22" || wug == "49")
            {
                vQuanLyKhoThuong1.Visible = true;
                vTTHN.Visible = false;
                vMEG.Visible = false;
            }

            if (wug == "49")
            {
                vTTHNKhachHang.Visible = false;
                vMEGHangXuatKH.Visible = false;
            }

            if (wug == "17")
            {
                vMEG.Visible = false;
            }
            if (wug == "26") // ALSE
            {
                vTTHN.Visible = false;
                vTTHX.Visible = false;
                vMEG.Visible = false;
                vChamCong.Visible = true;
                vQuanLyHangNhap.Visible = false;
                vQuanLyHangXuat.Visible = false;
            }

            if (wug == "27" || wug == "29") // 27 = FDI, 28 = MLT, 29 = ATT,
            {
                vMEG.Visible = true;
                vTTHNKhachHang.Visible = false;
                vMEGHangXuatKH.Visible = false;
                //if (path[0].Replace("/", "") != "TrangThaiHangNhap")
                //{
                //Response.Redirect("~/TrangThaiHangNhap.aspx");
                //Response.Redirect("~/QuanLyKHVC3.aspx");
                //}
            }
            if (uid == "136" || uid == "141") //thuynd && thachnt
            {
                vQLBC.Visible = true;
            }

            //phân quyền quản lý vât tư
            if (uid == "1" || uid == "103" || uid == "142" || uid == "116")// admin && Mrs.Hương && Mr.Toản && Mr.Bắc
            {
                vDanhSachVatTu.Visible = true;
                vNhapVatTu.Visible = true;
                vDeNghiCapPhat.Visible = true;
            }
            else
            {
                vDanhSachVatTu.Visible = false;
                vNhapVatTu.Visible = false;
                vDeNghiCapPhat.Visible = false;
            }

            //}
            //
            // vDSLoi.Visible = false;
            if (uid == "145") // ASG PP
            {
                vMEG.Visible = false;
                vTTHN.Visible = false;
                vQuanLyKhoThuong1.Visible = true;
                if (path[0].Replace("/", "") != "ReportQuanLyKhoThuong")
                {
                    Response.Redirect("~/ReportQuanLyKhoThuong.aspx");
                }
            }
            if (wug == "28") // 28 = SAFO
            {
                vQuanLyKhoThuong.Visible = true;
                vMEG.Visible = false;
                vExportSafo.Visible = true;

                // vTTHX.Visible = true; // Hàng Xuất
                if (path[0].Replace("/", "") == "TrangThaiHangNhap"
                 || path[0].Replace("/", "") == "QuanLyKHVC"
                 || path[0].Replace("/", "") == "TruyVanDNN"
                 || path[0].Replace("/", "") == "TruyVanHangXuat"
                 || path[0].Replace("/", "") == "ReportQuanLyKhoThuong"
                 || path[0].Replace("/", "") == "DanhSachDNN"
                    )
                {
                }
                else
                {
                    Response.Redirect("~/DanhSachDNN.aspx");
                }
            }
            if (uid == "151") // ha test
            {
                vThanhToan.Visible = false;
                DGR.Visible = false;
                vChamCong.Visible = false; // Chấm Công
                vXBC.Visible = false; // Xuất Báo Cáo
                vTTHX.Visible = false; // Hàng Xuất
                vDVAIPS.Visible = false;
                vThongBao.Visible = false;
                vDSLoi.Visible = false;
                vQLBC.Visible = false;
                vKSHSC.Visible = false;
                vInputExcel.Visible = false;
                vQuanLyKhoThuong.Visible = false;
                vQuanLyLogistic.Visible = false;
                vDistribution.Visible = false;
                vTruck.Visible = false;
                vTruckTraCuu.Visible = false;
                vTruckGiaBan.Visible = false;
                vTruckGiaMua.Visible = false;
                vTruckKhachHang.Visible = false;
                vCongViec.Visible = false;
                vQuanLyHangNhap.Visible = false;
                vQuanLyHangXuat.Visible = false;
                vDanhSachVatTu.Visible = false;
                vNhapVatTu.Visible = false;
                vDeNghiCapPhat.Visible = false;
                vOtherTool.Visible = false;
                if (path[0].Replace("/", "") != "QuanLyHangNhap")
                {
                    Response.Redirect("~/QuanLyHangNhap.aspx");
                }
            }
            if (wug == "30") //30 = KNT.ITM
            {
                vQuanLyHangNhap.Visible = true;
            }

            if (wug == "32") // 32 = ENL
            {
                vTTHN.Visible = false;
                if (path[0].Replace("/", "") != "QuanLyKHVC")
                {
                    Response.Redirect("~/QuanLyKHVC.aspx");
                }
            }
            if (wug == "35") //35 = EI.TOY
            {
                vTTHN.Visible = false;
                vMEG.Visible = false;
                vQuanLyKhoThuong.Visible = true;
                if (path[0].Replace("/", "") != "ReportQuanLyKhoThuong")
                {
                    Response.Redirect("~/ReportQuanLyKhoThuong.aspx");
                }
            }

            if (wug == "33")// 33 = JUSDA,
            {
                vTTHN.Visible = true;
                vMEG.Visible = false;
                vQuanLyKhoThuong.Visible = false;
                vMEGHangXuatKH.Visible = false;
                vTTHNKhachHang.Visible = false;
            }

            if (wug == "34") // 34 = DOLPHIN
            {
                vTTHN.Visible = false;
                if (path[0].Replace("/", "") == "QuanLyKHVC"
                     || path[0].Replace("/", "") == "DanhSachDNN"
                     || path[0].Replace("/", "") == "TruyVanDNN"
                    || path[0].Replace("/", "") == "TruyVanHangXuat")
                {
                }
                else
                {
                    Response.Redirect("~/QuanLyKHVC.aspx");
                }
            }
            if (wug == "6") // 6 = EI
            {
                vQuanLyKhoThuong2.Visible = true;
                vTTHN.Visible = false;
                vMEG.Visible = false;
                if (path[0].Replace("/", "") == "QuanLyKHVC"
                     || path[0].Replace("/", "") == "TrangThaiHangNhap"
                     || path[0].Replace("/", "") == "ReportQuanLyKhoThuong"
                     || path[0].Replace("/", "") == "XemCanDIM"
                     || path[0].Replace("/", "") == "TruyVanHangXuat"
                     || path[0].Replace("/", "") == "TruyVanDNN"
                     || path[0].Replace("/", "") == "QuanLyHangXuatKhachHang"
                     || path[0].Replace("/", "") == "DownloadFile"
                     || path[0].Replace("/", "") == "DanhSachDNN"
                   )
                {
                }
                else
                {
                    Response.Redirect("~/QuanLyHangXuatKhachHang.aspx");
                }
            }
            if (wug == "11" || wug == "36") // 11 = SMK, 36 = SEMV
            {
                vQuanLyKhoThuong2.Visible = true;
                vQuanLyLogistic.Visible = true;
            }
            if (wug == "37") // 37 = EFL, 38 = KWE
            {
                vTTHN.Visible = false;
                vMEG.Visible = true;
                vExportEFL.Visible = false;
                //vTraCuuDim.Visible = true;
                if (path[0].Replace("/", "") == "QuanLyKHVC2"
                    || path[0].Replace("/", "") == "DanhSachDNN"
                    || path[0].Replace("/", "") == "TruyVanDNN"
                    || path[0].Replace("/", "") == "TruyVanHangXuat"
                    || path[0].Replace("/", "") == "XemCanDIM"
                    )
                {

                }
                else
                {
                    Response.Redirect("~/QuanLyKHVC2.aspx");
                }
            }

            if (wug == "38") //38 = KWE
            {
                vTTHN.Visible = false;
                vMEG.Visible = false;
                vExportEFL.Visible = true;
                //if (path[0].Replace("/", "") == "QuanLyKHVC2"
                //    || path[0].Replace("/", "") == "DanhSachDNN"
                //    || path[0].Replace("/", "") == "TruyVanDNN"
                //    || path[0].Replace("/", "") == "TruyVanHangXuat"
                //    )
                //{

                //}
                //else
                //{
                //    Response.Redirect("~/QuanLyKHVC2.aspx");
                //}
            }

            if (wug == "40" || wug == "39" || wug == "3") //Khách Hàng ECL và HELL
            {
                vTraCuuDim.Visible = true;
                vMEG.Visible = false;
                vTTHN.Visible = false;
            }

            if (wug == "41") //APX
            {
                vExportAPX.Visible = true;
                vTTHN.Visible = false;
                vMEG.Visible = false;
            }

            if (wug == "42") //WNC
            {
                vQuanLyKhoHang.Visible = true;
                vTTHN.Visible = false;
                vMEG.Visible = false;
            }

            if (wug == "47")
            {
                vTTHN.Visible = false;
                vMEG.Visible = false;
                vMEGHangXuatKH.Visible = true;
            }


            if (wug == "48") // KUn
            {
                vTTHN.Visible = false;
                vMEG.Visible = false;
                vMEGHangXuatKH.Visible = true;
                vTTHNKhachHang.Visible = false;
            }

        }

        private void Session_Start(object sender, EventArgs e)
        {
            Session.Timeout = 1200;
        }
    }
}