﻿$(() => {
    //alert(1);
    fncClick();
})
 function fncClick() {
    $("#btn-tracuu-ncts").click(function () {
        let _mawb = $("#input-mawb").val();
        let _hawb = $("#input-hawb").val();

        if (_mawb == "" && _hawb == "") {
            alert("Vui lòng nhập số mawb hoặc số hawb để tra cứu thông tin!");
            return;
        }

        // Tra cứu hàng xuất
        if (_mawb != "" && _hawb == "") {
            fetchDataHangXuat(_mawb)
        }
        
    })
}

async function fetchDataHangXuat(mawb) {
    try {
        const response = await fetch('http://api.ncts.vn:1201/ALS/AcceptanceSearch', {
            method: 'POST', // hoặc GET, PUT, DELETE...
            headers: {
                'Content-Type': 'application/json',
                'X-API-Key': 'b01087301fba4f59f965443ada2980bd'
            },
            body: JSON.stringify({
                "AWB": mawb
            })
        });

        const data = await response.json();
        console.log(data);
    } catch (error) {
        console.error('Lỗi khi gọi API:', error);
    }
}