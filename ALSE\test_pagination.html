<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Pagination - QuanLyChiHo</title>
    <!-- Bootstrap 3.x CSS -->
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/custom/QuanLyChiHo.css">
</head>
<body>
    <div class="container">
        <h2>Test Pagination - Quản Lý Chi Hộ</h2>
        
        <!-- Pagination Controls -->
        <div class="row" id="div-chiho-pagination-controls">
            <div class="col-sm-6">
                <div class="form-group">
                    <label for="select-chiho-items-per-page">Hiển thị:</label>
                    <select class="form-control input-sm" id="select-chiho-items-per-page" style="width: 100px; display: inline-block;">
                        <option value="50" selected>50</option>
                        <option value="100">100</option>
                        <option value="200">200</option>
                        <option value="500">500</option>
                        <option value="1000">1000</option>
                    </select>
                    <span>mục mỗi trang</span>
                </div>
            </div>
            <div class="col-sm-6">
                <div class="pagination-info text-right">
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-default btn-sm" id="btn-chiho-prev-page" disabled>
                            <i class="fa fa-chevron-left"></i> Trước
                        </button>
                        <button type="button" class="btn btn-default btn-sm" id="btn-chiho-page-info" disabled>
                            Trang 1 / 1
                        </button>
                        <button type="button" class="btn btn-default btn-sm" id="btn-chiho-next-page" disabled>
                            Sau <i class="fa fa-chevron-right"></i>
                        </button>
                    </div>
                    <div class="pagination-summary" style="margin-top: 5px;">
                        <small class="text-muted" id="span-chiho-pagination-summary">Hiển thị 0 - 0 của 0 mục</small>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Test Table -->
        <table class="table table-bordered" id="tbl-chiho">
            <thead>
                <tr>
                    <td><input type="checkbox" id="cb-print-all" class="td-checkbox" value="ALL" /></td>
                    <td>NCC</td>
                    <td>Loại hình</td>
                    <td>Khách hàng</td>
                    <td>AWB/BILL</td>
                    <td>Ký hiệu HĐ</td>
                    <td>Số HĐ</td>
                    <td>Người bán</td>
                    <td>Người mua</td>
                    <td>Tên phí</td>
                    <td>Số tiền thanh toán<br>(Sau VAT)</td>
                    <td>Số tiền trước VAT</td>
                    <td>Ngày CK</td>
                    <td>Số đề nghị thanh toán</td>
                    <td>Ghi chú</td>
                    <td>Ngày tạo</td>
                    <td class="td-chucnang">Chức năng</td>
                </tr>
            </thead>
            <tbody>
                <!-- Test data will be generated by JavaScript -->
            </tbody>
        </table>
        
        <button type="button" class="btn btn-primary" id="btn-generate-test-data">Tạo dữ liệu test (120 rows)</button>
    </div>

    <!-- jQuery -->
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/1.12.4/jquery.min.js"></script>
    <!-- Bootstrap 3.x JS -->
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/js/bootstrap.min.js"></script>
    
    <script>
        // Pagination Variables
        var paginationData = {
            currentPage: 1,
            itemsPerPage: 50,
            totalItems: 0,
            totalPages: 0,
            allRows: []
        };

        // Pagination Functions (simplified for testing)
        function initializePagination() {
            $("#select-chiho-items-per-page").on("change", function() {
                paginationData.itemsPerPage = parseInt($(this).val());
                paginationData.currentPage = 1;
                updatePagination();
            });
            
            $("#btn-chiho-prev-page").on("click", function() {
                if (paginationData.currentPage > 1) {
                    paginationData.currentPage--;
                    updatePagination();
                }
            });
            
            $("#btn-chiho-next-page").on("click", function() {
                if (paginationData.currentPage < paginationData.totalPages) {
                    paginationData.currentPage++;
                    updatePagination();
                }
            });
        }

        function setPaginationData(rows) {
            paginationData.allRows = rows;
            paginationData.totalItems = rows.length;
            paginationData.totalPages = Math.ceil(paginationData.totalItems / paginationData.itemsPerPage);
            paginationData.currentPage = 1;
            updatePagination();
        }

        function updatePagination() {
            var startIndex = (paginationData.currentPage - 1) * paginationData.itemsPerPage;
            var endIndex = Math.min(startIndex + paginationData.itemsPerPage, paginationData.totalItems);
            
            $("#tbl-chiho tbody tr").hide();
            
            for (var i = startIndex; i < endIndex; i++) {
                if (paginationData.allRows[i]) {
                    $(paginationData.allRows[i]).show();
                }
            }
            
            updatePaginationInfo();
            updatePaginationButtons();
        }

        function updatePaginationInfo() {
            var startIndex = (paginationData.currentPage - 1) * paginationData.itemsPerPage + 1;
            var endIndex = Math.min(paginationData.currentPage * paginationData.itemsPerPage, paginationData.totalItems);
            
            $("#btn-chiho-page-info").text("Trang " + paginationData.currentPage + " / " + paginationData.totalPages);
            
            if (paginationData.totalItems > 0) {
                $("#span-chiho-pagination-summary").text("Hiển thị " + startIndex + " - " + endIndex + " của " + paginationData.totalItems + " mục");
            } else {
                $("#span-chiho-pagination-summary").text("Hiển thị 0 - 0 của 0 mục");
            }
        }

        function updatePaginationButtons() {
            $("#btn-chiho-prev-page").prop("disabled", paginationData.currentPage <= 1);
            $("#btn-chiho-next-page").prop("disabled", paginationData.currentPage >= paginationData.totalPages || paginationData.totalPages === 0);
        }

        // Generate test data
        function generateTestData() {
            var html_body = "";
            for (var i = 1; i <= 120; i++) {
                html_body += "<tr class='tr-main'>";
                html_body += "<td><input type='checkbox' /></td>";
                html_body += "<td>NCC" + i + "</td>";
                html_body += "<td>" + (i % 3 === 0 ? "IMP" : i % 3 === 1 ? "EXP" : "LOG") + "</td>";
                html_body += "<td>Khách hàng " + i + "</td>";
                html_body += "<td>AWB" + String(i).padStart(6, '0') + "</td>";
                html_body += "<td>KH" + i + "</td>";
                html_body += "<td>HD" + String(i).padStart(8, '0') + "</td>";
                html_body += "<td>Người bán " + i + "</td>";
                html_body += "<td>ALSE</td>";
                html_body += "<td>Phí chứng từ hàng nhập</td>";
                html_body += "<td>" + (i * 100000).toLocaleString() + "</td>";
                html_body += "<td>" + (i * 90000).toLocaleString() + "</td>";
                html_body += "<td>01/01/2024</td>";
                html_body += "<td>DNTT" + String(i).padStart(6, '0') + "</td>";
                html_body += "<td>Ghi chú " + i + "</td>";
                html_body += "<td>01/01/2024</td>";
                html_body += "<td class='td-chucnang'><a class='label label-info'>Đính kèm</a> <a class='label label-success'>Xem QR</a></td>";
                html_body += "</tr>";
            }
            
            $("#tbl-chiho tbody").empty().append(html_body);
            var mainRows = $("#tbl-chiho tbody tr.tr-main").toArray();
            setPaginationData(mainRows);
        }

        // Initialize on document ready
        $(document).ready(function() {
            initializePagination();
            
            $("#btn-generate-test-data").on("click", function() {
                generateTestData();
            });
            
            // Highlight functionality
            $("#tbl-chiho").on("click", "tr td", function () {
                $(".tr-highlight").removeClass("tr-highlight");
                $(this).closest("tr").addClass("tr-highlight");
            });
        });
    </script>
</body>
</html>
