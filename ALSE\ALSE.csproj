﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{5D106DEE-D6F7-4E9A-AA6A-AF57B8B7F37B}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>ALSE</RootNamespace>
    <AssemblyName>ALSE</AssemblyName>
    <TargetFrameworkVersion>v4.0</TargetFrameworkVersion>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <UpgradeBackupLocation>
    </UpgradeBackupLocation>
    <OldToolsVersion>4.0</OldToolsVersion>
    <UseIISExpress>false</UseIISExpress>
    <IISExpressSSLPort />
    <IISExpressAnonymousAuthentication />
    <IISExpressWindowsAuthentication />
    <IISExpressUseClassicPipelineMode />
    <UseGlobalApplicationHostFile />
    <Use64BitIISExpress />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <ExcludeApp_Data>true</ExcludeApp_Data>
    <DeployIisAppPath>Default Web Site/ALSE</DeployIisAppPath>
    <FilesToIncludeForPublish>AllFilesInTheProject</FilesToIncludeForPublish>
    <UseVSHostingProcess>true</UseVSHostingProcess>
    <PlatformTarget>AnyCPU</PlatformTarget>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>0</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="AjaxControlToolkit, Version=********, Culture=neutral, PublicKeyToken=28f01b0e84b6d53e, processorArchitecture=MSIL">
      <HintPath>..\packages\AjaxControlToolkit.********\lib\net40\AjaxControlToolkit.dll</HintPath>
    </Reference>
    <Reference Include="BouncyCastle.Crypto, Version=1.7.4137.9688, Culture=neutral, PublicKeyToken=a4292a325f69b123, processorArchitecture=MSIL">
      <HintPath>..\packages\BouncyCastle.1.7.0\lib\Net40-Client\BouncyCastle.Crypto.dll</HintPath>
    </Reference>
    <Reference Include="CKEditor.NET, Version=*******, Culture=neutral, PublicKeyToken=e379cdf2f8354999, processorArchitecture=MSIL">
      <HintPath>..\packages\CKEditor.net.1.0.0\lib\CKEditor.NET.dll</HintPath>
    </Reference>
    <Reference Include="Common.Logging, Version=2.0.0.0, Culture=neutral, PublicKeyToken=af08829b84f0328e, processorArchitecture=MSIL">
      <HintPath>..\packages\Common.Logging.2.0.0\lib\2.0\Common.Logging.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="DevExpress.BonusSkins.v14.2, Version=14.2.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>E:\devexpress dll\DevExpress.BonusSkins.v14.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Charts.v14.2.Core, Version=14.2.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>E:\devexpress dll\DevExpress.Charts.v14.2.Core.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.CodeParser.v14.2, Version=14.2.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>E:\devexpress dll\DevExpress.CodeParser.v14.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Data.v14.2, Version=14.2.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>E:\devexpress dll\DevExpress.Data.v14.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.DataAccess.v14.2, Version=14.2.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>E:\devexpress dll\DevExpress.DataAccess.v14.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.DataAccess.v14.2.UI, Version=14.2.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>E:\devexpress dll\DevExpress.DataAccess.v14.2.UI.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.ExpressApp.v14.2, Version=14.2.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>E:\devexpress dll\DevExpress.ExpressApp.v14.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.ExpressApp.Win.v14.2, Version=14.2.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>E:\devexpress dll\DevExpress.ExpressApp.Win.v14.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.ExpressApp.Xpo.v14.2, Version=14.2.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>E:\devexpress dll\DevExpress.ExpressApp.Xpo.v14.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Office.v14.2.Core, Version=14.2.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>E:\devexpress dll\DevExpress.Office.v14.2.Core.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Persistent.Base.v14.2, Version=14.2.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>E:\devexpress dll\DevExpress.Persistent.Base.v14.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.PivotGrid.v14.2.Core, Version=14.2.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>E:\devexpress dll\DevExpress.PivotGrid.v14.2.Core.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Printing.v14.2.Core, Version=14.2.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>E:\devexpress dll\DevExpress.Printing.v14.2.Core.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.RichEdit.v14.2.Core, Version=14.2.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>E:\devexpress dll\DevExpress.RichEdit.v14.2.Core.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Sparkline.v14.2.Core, Version=14.2.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>E:\devexpress dll\DevExpress.Sparkline.v14.2.Core.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.SpellChecker.v14.2.Core, Version=14.2.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>E:\devexpress dll\DevExpress.SpellChecker.v14.2.Core.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Utils.v14.2, Version=14.2.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>E:\devexpress dll\DevExpress.Utils.v14.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Utils.v14.2.UI, Version=14.2.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>E:\devexpress dll\DevExpress.Utils.v14.2.UI.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Web.ASPxHtmlEditor.v14.2, Version=14.2.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>E:\devexpress dll\DevExpress.Web.ASPxHtmlEditor.v14.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Web.ASPxPivotGrid.v14.2, Version=14.2.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>E:\devexpress dll\DevExpress.Web.ASPxPivotGrid.v14.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Web.ASPxRichEdit.v14.2, Version=14.2.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>E:\devexpress dll\DevExpress.Web.ASPxRichEdit.v14.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Web.ASPxSpellChecker.v14.2, Version=14.2.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>E:\devexpress dll\DevExpress.Web.ASPxSpellChecker.v14.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Web.ASPxThemes.v14.2, Version=14.2.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>E:\devexpress dll\DevExpress.Web.ASPxThemes.v14.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Web.v14.2, Version=14.2.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>E:\devexpress dll\DevExpress.Web.v14.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Xpo.v14.2, Version=14.2.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>E:\devexpress dll\DevExpress.Xpo.v14.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Xpo.v14.2.Web, Version=14.2.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>E:\devexpress dll\DevExpress.Xpo.v14.2.Web.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraBars.v14.2, Version=14.2.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>E:\devexpress dll\DevExpress.XtraBars.v14.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraCharts.v14.2, Version=14.2.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>E:\devexpress dll\DevExpress.XtraCharts.v14.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraCharts.v14.2.Extensions, Version=14.2.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>E:\devexpress dll\DevExpress.XtraCharts.v14.2.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraCharts.v14.2.Web, Version=14.2.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>E:\devexpress dll\DevExpress.XtraCharts.v14.2.Web.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraCharts.v14.2.Wizard, Version=14.2.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>E:\devexpress dll\DevExpress.XtraCharts.v14.2.Wizard.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraEditors.v14.2, Version=14.2.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>E:\devexpress dll\DevExpress.XtraEditors.v14.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraGauges.v14.2.Core, Version=14.2.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>E:\devexpress dll\DevExpress.XtraGauges.v14.2.Core.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraGrid.v14.2, Version=14.2.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>E:\devexpress dll\DevExpress.XtraGrid.v14.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraLayout.v14.2, Version=14.2.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>E:\devexpress dll\DevExpress.XtraLayout.v14.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraNavBar.v14.2, Version=14.2.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>E:\devexpress dll\DevExpress.XtraNavBar.v14.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraPivotGrid.v14.2, Version=14.2.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>E:\devexpress dll\DevExpress.XtraPivotGrid.v14.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraPrinting.v14.2, Version=14.2.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>E:\devexpress dll\DevExpress.XtraPrinting.v14.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraReports.v14.2, Version=14.2.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>E:\devexpress dll\DevExpress.XtraReports.v14.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraReports.v14.2.Extensions, Version=14.2.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>E:\devexpress dll\DevExpress.XtraReports.v14.2.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraReports.v14.2.Web, Version=14.2.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>E:\devexpress dll\DevExpress.XtraReports.v14.2.Web.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraRichEdit.v14.2, Version=14.2.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>E:\devexpress dll\DevExpress.XtraRichEdit.v14.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraTreeList.v14.2, Version=14.2.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>E:\devexpress dll\DevExpress.XtraTreeList.v14.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraVerticalGrid.v14.2, Version=14.2.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>E:\devexpress dll\DevExpress.XtraVerticalGrid.v14.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraWizard.v14.2, Version=14.2.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>E:\devexpress dll\DevExpress.XtraWizard.v14.2.dll</HintPath>
    </Reference>
    <Reference Include="DocumentFormat.OpenXml, Version=2.5.5631.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\DocumentFormat.OpenXml.2.5\lib\DocumentFormat.OpenXml.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="DotNetZip, Version=********, Culture=neutral, PublicKeyToken=6583c7c814667745, processorArchitecture=MSIL">
      <HintPath>..\packages\DotNetZip.1.16.0\lib\net40\DotNetZip.dll</HintPath>
    </Reference>
    <Reference Include="EPPlus, Version=*******, Culture=neutral, PublicKeyToken=ea159fdaa78159a1, processorArchitecture=MSIL">
      <HintPath>..\packages\EPPlus.4.1.0\lib\net40\EPPlus.dll</HintPath>
    </Reference>
    <Reference Include="EPPlus.Interfaces, Version=*******, Culture=neutral, PublicKeyToken=a694d7f3b0907a61, processorArchitecture=MSIL">
      <HintPath>..\packages\EPPlus.Interfaces.6.0.0\lib\net35\EPPlus.Interfaces.dll</HintPath>
    </Reference>
    <Reference Include="EPPlus.System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=2308d35469c9bac0, processorArchitecture=MSIL">
      <HintPath>..\packages\EPPlus.System.Drawing.6.0.0\lib\net35\EPPlus.System.Drawing.dll</HintPath>
    </Reference>
    <Reference Include="Google.Apis, Version=1.10.0.25332, Culture=neutral, PublicKeyToken=4b01fa6e34db77ab, processorArchitecture=MSIL">
      <HintPath>..\packages\Google.Apis.1.10.0\lib\net40\Google.Apis.dll</HintPath>
    </Reference>
    <Reference Include="Google.Apis.Auth, Version=1.10.0.25333, Culture=neutral, PublicKeyToken=4b01fa6e34db77ab, processorArchitecture=MSIL">
      <HintPath>..\packages\Google.Apis.Auth.1.10.0\lib\net40\Google.Apis.Auth.dll</HintPath>
    </Reference>
    <Reference Include="Google.Apis.Auth.PlatformServices, Version=1.10.0.25333, Culture=neutral, PublicKeyToken=4b01fa6e34db77ab, processorArchitecture=MSIL">
      <HintPath>..\packages\Google.Apis.Auth.1.10.0\lib\net40\Google.Apis.Auth.PlatformServices.dll</HintPath>
    </Reference>
    <Reference Include="Google.Apis.Core, Version=1.10.0.25331, Culture=neutral, PublicKeyToken=4b01fa6e34db77ab, processorArchitecture=MSIL">
      <HintPath>..\packages\Google.Apis.Core.1.10.0\lib\portable-net40+sl50+win+wpa81+wp80\Google.Apis.Core.dll</HintPath>
    </Reference>
    <Reference Include="Google.Apis.PlatformServices, Version=1.10.0.25332, Culture=neutral, PublicKeyToken=4b01fa6e34db77ab, processorArchitecture=MSIL">
      <HintPath>..\packages\Google.Apis.1.10.0\lib\net40\Google.Apis.PlatformServices.dll</HintPath>
    </Reference>
    <Reference Include="Google.Apis.Sheets.v4, Version=1.50.0.2259, Culture=neutral, PublicKeyToken=4b01fa6e34db77ab, processorArchitecture=MSIL">
      <HintPath>..\packages\Google.Apis.Sheets.v4.1.50.0.2259\lib\net40\Google.Apis.Sheets.v4.dll</HintPath>
    </Reference>
    <Reference Include="HtmlAgilityPack">
      <HintPath>..\packages\HtmlAgilityPack.1.4.9\lib\Net40\HtmlAgilityPack.dll</HintPath>
    </Reference>
    <Reference Include="itextsharp, Version=5.4.5.0, Culture=neutral, PublicKeyToken=8354ae6d2174ddca, processorArchitecture=MSIL">
      <HintPath>..\packages\iTextSharp.5.4.5\lib\itextsharp.dll</HintPath>
    </Reference>
    <Reference Include="log4net, Version=2.0.9.0, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a, processorArchitecture=MSIL">
      <HintPath>..\packages\log4net.2.0.10\lib\net40\log4net.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp">
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.IO.RecyclableMemoryStream, Version=1.4.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IO.RecyclableMemoryStream.1.4.1\lib\net40\Microsoft.IO.RecyclableMemoryStream.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Threading.Tasks, Version=1.0.12.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.Async.1.0.168\lib\net40\Microsoft.Threading.Tasks.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Threading.Tasks.Extensions, Version=1.0.12.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.Async.1.0.168\lib\net40\Microsoft.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Threading.Tasks.Extensions.Desktop, Version=1.0.168.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.Async.1.0.168\lib\net40\Microsoft.Threading.Tasks.Extensions.Desktop.dll</HintPath>
    </Reference>
    <Reference Include="MimeTypeMap, Version=2.1.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\MediaTypeMap.2.1.0.0\lib\net40\MimeTypeMap.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=7.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.7.0.1\lib\net40\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="PdfSharp, Version=1.32.3057.0, Culture=neutral, PublicKeyToken=f94615aa0424f9eb, processorArchitecture=MSIL">
      <HintPath>..\packages\PDFsharp.1.32.3057.0\lib\net20\PdfSharp.dll</HintPath>
    </Reference>
    <Reference Include="PdfSharp.Charting, Version=1.32.3057.0, Culture=neutral, PublicKeyToken=f94615aa0424f9eb, processorArchitecture=MSIL">
      <HintPath>..\packages\PDFsharp.1.32.3057.0\lib\net20\PdfSharp.Charting.dll</HintPath>
    </Reference>
    <Reference Include="PresentationCore" />
    <Reference Include="PresentationFramework" />
    <Reference Include="RestSharp, Version=104.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RestSharp.104.0\lib\net4\RestSharp.dll</HintPath>
    </Reference>
    <Reference Include="Spire.Pdf, Version=8.7.2.0, Culture=neutral, PublicKeyToken=663f351905198cb3, processorArchitecture=MSIL">
      <HintPath>..\packages\FreeSpire.XLS.12.7.0\lib\net40\Spire.Pdf.dll</HintPath>
    </Reference>
    <Reference Include="Spire.XLS, Version=12.7.0.0, Culture=neutral, PublicKeyToken=663f351905198cb3, processorArchitecture=MSIL">
      <HintPath>..\packages\FreeSpire.XLS.12.7.0\lib\net40\Spire.XLS.dll</HintPath>
    </Reference>
    <Reference Include="Spring.Rest, Version=1.1.0.5340, Culture=neutral, PublicKeyToken=65e474d141e25e07, processorArchitecture=MSIL">
      <HintPath>..\packages\Spring.Rest.1.1.0\lib\net40\Spring.Rest.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System">
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data" />
    <Reference Include="System.Core">
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.IO, Version=2.6.10.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.1.1.10\lib\net40\System.IO.dll</HintPath>
    </Reference>
    <Reference Include="System.Net" />
    <Reference Include="System.Net.Http, Version=2.2.29.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Net.Http.2.2.29\lib\net40\System.Net.Http.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http.Extensions, Version=2.2.29.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Net.Http.2.2.29\lib\net40\System.Net.Http.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http.Primitives, Version=2.2.29.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Net.Http.2.2.29\lib\net40\System.Net.Http.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http.WebRequest, Version=2.2.29.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Net.Http.2.2.29\lib\net40\System.Net.Http.WebRequest.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime, Version=2.6.10.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.1.1.10\lib\net40\System.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Security" />
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.Threading.Tasks, Version=2.6.10.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.1.1.10\lib\net40\System.Threading.Tasks.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Web" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration">
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Web.Services" />
    <Reference Include="System.EnterpriseServices" />
    <Reference Include="System.Web.DynamicData" />
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="WindowsBase" />
    <Reference Include="Zlib.Portable, Version=1.11.0.0, Culture=neutral, PublicKeyToken=431cba815f6a8b5b, processorArchitecture=MSIL">
      <HintPath>..\packages\Zlib.Portable.Signed.1.11.0\lib\portable-net4+sl5+wp8+win8+wpa81+MonoTouch+MonoAndroid\Zlib.Portable.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Admin\QuanLyNhanVien.aspx" />
    <Content Include="Admin\QuanLyNhomTaiKhoan.aspx" />
    <Content Include="Admin\QuanLyPhanQuyen.aspx" />
    <Content Include="App_Data\pdf2json.exe" />
    <Content Include="AWBView.aspx" />
    <Content Include="BaoCaoOther.aspx" />
    <Content Include="BilldingvsUpdate.aspx" />
    <Content Include="ChamCong\BangChamCongLoi.aspx" />
    <Content Include="ChamCong\BangTin.aspx" />
    <Content Include="ChamCong\DiMuon.aspx" />
    <Content Include="ChamCong\XuatBangChamCong.aspx" />
    <Content Include="ChuyenPhatNhanhCPN.aspx" />
    <Content Include="ChuyenPhatNhanh_V2.aspx" />
    <Content Include="CongViec\BangTin.aspx" />
    <Content Include="CPNView.aspx" />
    <Content Include="css\bootstrap-tagsinput.css" />
    <Content Include="css\custom\AWBView.css" />
    <Content Include="css\custom\BangChamCong.css" />
    <Content Include="css\custom\BangChamCongLoi.css" />
    <Content Include="css\custom\BaoCaoOther.css" />
    <Content Include="css\custom\BilldingvsUpdate.css" />
    <Content Include="css\custom\ChamCong-BangTin.css" />
    <Content Include="css\custom\ChamCong-TrangThai.css" />
    <Content Include="css\custom\ChamCong-XuatBangChamCong.css" />
    <Content Include="css\custom\ChuyenPhatNhanhCPN.css" />
    <Content Include="css\custom\ChuyenPhatNhanh_V2.css" />
    <Content Include="css\custom\CongViec-BangTin.css" />
    <Content Include="css\custom\CPNView.css" />
    <Content Include="css\custom\DangKyVaoRa.css" />
    <Content Include="css\custom\DanhSachVatTu.css" />
    <Content Include="css\custom\DeNghiCapPhat.css" />
    <Content Include="css\custom\Distribution-Customers.css" />
    <Content Include="css\custom\Distribution-DeliveryOrders.css" />
    <Content Include="css\custom\Distribution-Products.css" />
    <Content Include="css\custom\Distribution-Route.css" />
    <Content Include="css\custom\GetZalo.css" />
    <Content Include="css\custom\inlabel.css" />
    <Content Include="css\custom\InputExcelBook.css" />
    <Content Include="css\custom\InputExcelBookVsip.css" />
    <Content Include="css\custom\KeHoachDetails.css" />
    <Content Include="css\custom\KhoVsip1.css" />
    <Content Include="css\custom\KhoVsip2.css" />
    <Content Include="css\custom\OPS.css" />
    <Content Include="css\custom\PODView.css" />
    <Content Include="css\custom\printLabel.css" />
    <Content Include="css\custom\QRCode.css" />
    <Content Include="css\custom\QuanLyAPPLEKeHoach.css" />
    <Content Include="css\custom\QuanLyAPPLEXuatKho.css" />
    <Content Include="css\custom\QuanLyChamCong-DiMuon.css" />
    <Content Include="css\custom\QuanLyChiHo.css" />
    <Content Include="css\custom\QuanLyChiHoNCC.css" />
    <Content Include="css\custom\QuanLyChuyenXeCPN.css" />
    <Content Include="css\custom\QuanLyCongVanHQ.css" />
    <Content Include="css\custom\QuanLyGiaoNhanDOC.css" />
    <Content Include="css\custom\QuanLyHangNhap.css" />
    <Content Include="css\custom\QuanLyHangNhapKhachHang.css" />
    <Content Include="css\custom\QuanLyHangXuat.css" />
    <Content Include="css\custom\QuanLyHangXuatKhachHang.css" />
    <Content Include="css\custom\QuanLyInTem.css" />
    <Content Include="css\custom\QuanLyKhoFM.css" />
    <Content Include="css\custom\QuanLyKhoNhap.css" />
    <Content Include="css\custom\QuanLyLoi.css" />
    <Content Include="css\custom\QuanLyNhanVien.css" />
    <Content Include="css\custom\QuanLyNhapVatTu.css" />
    <Content Include="css\custom\QuanLyPOD.css" />
    <Content Include="css\custom\QuanLyPODView.css" />
    <Content Include="css\custom\QuanLySuatAn.css" />
    <Content Include="css\custom\QuanLySuatAnApp.css" />
    <Content Include="css\custom\QuanLyViTriKhoMoi.css" />
    <Content Include="css\custom\thanhtoan-baocao.css" />
    <Content Include="css\custom\thanhtoan-khachhangdoitac.css" />
    <Content Include="css\custom\TraCuuDienEDI.css" />
    <Content Include="css\custom\TraCuuDNN.css" />
    <Content Include="css\custom\TrienKhaiVanBan.css" />
    <Content Include="css\custom\TruckGiaBan.css" />
    <Content Include="css\custom\TruckGiaMua.css" />
    <Content Include="css\custom\TruckKhachHang.css" />
    <Content Include="css\custom\TruckTraCuu.css" />
    <Content Include="css\custom\TruckTuyen.css" />
    <Content Include="css\custom\TruyVanCPN.css" />
    <Content Include="css\custom\TruyVanDNN.css" />
    <Content Include="css\custom\TruyVanPo.css" />
    <Content Include="css\custom\UpdateFWD.css" />
    <Content Include="css\custom\XemAnhHoaDonXuat.css" />
    <Content Include="css\custom\XemCanDIM.css" />
    <Content Include="css\images\ui-bg_flat_0_888888_40x100.png" />
    <Content Include="css\images\ui-bg_flat_0_aaaaaa_40x100.png" />
    <Content Include="css\images\ui-bg_flat_75_ffffff_40x100.png" />
    <Content Include="css\images\ui-bg_glass_25_e1f0f5_1x400.png" />
    <Content Include="css\images\ui-bg_glass_55_444444_1x400.png" />
    <Content Include="css\images\ui-bg_glass_55_fbf9ee_1x400.png" />
    <Content Include="css\images\ui-bg_glass_65_ffffff_1x400.png" />
    <Content Include="css\images\ui-bg_glass_75_dadada_1x400.png" />
    <Content Include="css\images\ui-bg_glass_75_e6e6e6_1x400.png" />
    <Content Include="css\images\ui-bg_highlight-soft_75_cccccc_1x100.png" />
    <Content Include="css\images\ui-bg_inset-soft_95_fef1ec_1x100.png" />
    <Content Include="css\images\ui-icons_222222_256x240.png" />
    <Content Include="css\images\ui-icons_309bbf_256x240.png" />
    <Content Include="css\images\ui-icons_454545_256x240.png" />
    <Content Include="css\images\ui-icons_bf3030_256x240.png" />
    <Content Include="css\images\ui-icons_ffffff_256x240.png" />
    <Content Include="css\kendo.common.min.css" />
    <Content Include="css\kendo.mobile.all.min.css" />
    <Content Include="css\kendo.rtl.min.css" />
    <Content Include="css\kendo.silver.min.css" />
    <Content Include="css\print.min.css" />
    <Content Include="DangKyVaoRa.aspx" />
    <Content Include="DanhSachVatTu\DanhSachVatTu.aspx" />
    <Content Include="DanhSachVatTu\DeNghiCapPhat.aspx" />
    <Content Include="DanhSachVatTu\QuanLyNhapVatTu.aspx" />
    <Content Include="Distribution\DeliveryOrders.aspx" />
    <Content Include="Distribution\Route.aspx" />
    <Content Include="DownloadCacheFile.aspx" />
    <Content Include="Email_Temp\BangTinBaoCaoTemp.html" />
    <Content Include="Email_Temp\BangTinBaoCaoTrTemp.html" />
    <Content Include="ERP.aspx" />
    <Content Include="Extensions\ckeditor\adapters\jquery.js" />
    <Content Include="Extensions\ckeditor\build-config.js" />
    <Content Include="Extensions\ckeditor\ckeditor.js" />
    <Content Include="Extensions\ckeditor\config.js" />
    <Content Include="Extensions\ckeditor\contents.css" />
    <Content Include="Extensions\ckeditor\lang\en.js" />
    <Content Include="Extensions\ckeditor\lang\vi.js" />
    <Content Include="Extensions\ckeditor\plugins\a11yhelp\dialogs\a11yhelp.js" />
    <Content Include="Extensions\ckeditor\plugins\a11yhelp\dialogs\lang\af.js" />
    <Content Include="Extensions\ckeditor\plugins\a11yhelp\dialogs\lang\ar.js" />
    <Content Include="Extensions\ckeditor\plugins\a11yhelp\dialogs\lang\az.js" />
    <Content Include="Extensions\ckeditor\plugins\a11yhelp\dialogs\lang\bg.js" />
    <Content Include="Extensions\ckeditor\plugins\a11yhelp\dialogs\lang\ca.js" />
    <Content Include="Extensions\ckeditor\plugins\a11yhelp\dialogs\lang\cs.js" />
    <Content Include="Extensions\ckeditor\plugins\a11yhelp\dialogs\lang\cy.js" />
    <Content Include="Extensions\ckeditor\plugins\a11yhelp\dialogs\lang\da.js" />
    <Content Include="Extensions\ckeditor\plugins\a11yhelp\dialogs\lang\de-ch.js" />
    <Content Include="Extensions\ckeditor\plugins\a11yhelp\dialogs\lang\de.js" />
    <Content Include="Extensions\ckeditor\plugins\a11yhelp\dialogs\lang\el.js" />
    <Content Include="Extensions\ckeditor\plugins\a11yhelp\dialogs\lang\en-gb.js" />
    <Content Include="Extensions\ckeditor\plugins\a11yhelp\dialogs\lang\en.js" />
    <Content Include="Extensions\ckeditor\plugins\a11yhelp\dialogs\lang\eo.js" />
    <Content Include="Extensions\ckeditor\plugins\a11yhelp\dialogs\lang\es-mx.js" />
    <Content Include="Extensions\ckeditor\plugins\a11yhelp\dialogs\lang\es.js" />
    <Content Include="Extensions\ckeditor\plugins\a11yhelp\dialogs\lang\et.js" />
    <Content Include="Extensions\ckeditor\plugins\a11yhelp\dialogs\lang\eu.js" />
    <Content Include="Extensions\ckeditor\plugins\a11yhelp\dialogs\lang\fa.js" />
    <Content Include="Extensions\ckeditor\plugins\a11yhelp\dialogs\lang\fi.js" />
    <Content Include="Extensions\ckeditor\plugins\a11yhelp\dialogs\lang\fo.js" />
    <Content Include="Extensions\ckeditor\plugins\a11yhelp\dialogs\lang\fr-ca.js" />
    <Content Include="Extensions\ckeditor\plugins\a11yhelp\dialogs\lang\fr.js" />
    <Content Include="Extensions\ckeditor\plugins\a11yhelp\dialogs\lang\gl.js" />
    <Content Include="Extensions\ckeditor\plugins\a11yhelp\dialogs\lang\gu.js" />
    <Content Include="Extensions\ckeditor\plugins\a11yhelp\dialogs\lang\he.js" />
    <Content Include="Extensions\ckeditor\plugins\a11yhelp\dialogs\lang\hi.js" />
    <Content Include="Extensions\ckeditor\plugins\a11yhelp\dialogs\lang\hr.js" />
    <Content Include="Extensions\ckeditor\plugins\a11yhelp\dialogs\lang\hu.js" />
    <Content Include="Extensions\ckeditor\plugins\a11yhelp\dialogs\lang\id.js" />
    <Content Include="Extensions\ckeditor\plugins\a11yhelp\dialogs\lang\it.js" />
    <Content Include="Extensions\ckeditor\plugins\a11yhelp\dialogs\lang\ja.js" />
    <Content Include="Extensions\ckeditor\plugins\a11yhelp\dialogs\lang\km.js" />
    <Content Include="Extensions\ckeditor\plugins\a11yhelp\dialogs\lang\ko.js" />
    <Content Include="Extensions\ckeditor\plugins\a11yhelp\dialogs\lang\ku.js" />
    <Content Include="Extensions\ckeditor\plugins\a11yhelp\dialogs\lang\lt.js" />
    <Content Include="Extensions\ckeditor\plugins\a11yhelp\dialogs\lang\lv.js" />
    <Content Include="Extensions\ckeditor\plugins\a11yhelp\dialogs\lang\mk.js" />
    <Content Include="Extensions\ckeditor\plugins\a11yhelp\dialogs\lang\mn.js" />
    <Content Include="Extensions\ckeditor\plugins\a11yhelp\dialogs\lang\nb.js" />
    <Content Include="Extensions\ckeditor\plugins\a11yhelp\dialogs\lang\nl.js" />
    <Content Include="Extensions\ckeditor\plugins\a11yhelp\dialogs\lang\no.js" />
    <Content Include="Extensions\ckeditor\plugins\a11yhelp\dialogs\lang\oc.js" />
    <Content Include="Extensions\ckeditor\plugins\a11yhelp\dialogs\lang\pl.js" />
    <Content Include="Extensions\ckeditor\plugins\a11yhelp\dialogs\lang\pt-br.js" />
    <Content Include="Extensions\ckeditor\plugins\a11yhelp\dialogs\lang\pt.js" />
    <Content Include="Extensions\ckeditor\plugins\a11yhelp\dialogs\lang\ro.js" />
    <Content Include="Extensions\ckeditor\plugins\a11yhelp\dialogs\lang\ru.js" />
    <Content Include="Extensions\ckeditor\plugins\a11yhelp\dialogs\lang\si.js" />
    <Content Include="Extensions\ckeditor\plugins\a11yhelp\dialogs\lang\sk.js" />
    <Content Include="Extensions\ckeditor\plugins\a11yhelp\dialogs\lang\sl.js" />
    <Content Include="Extensions\ckeditor\plugins\a11yhelp\dialogs\lang\sq.js" />
    <Content Include="Extensions\ckeditor\plugins\a11yhelp\dialogs\lang\sr-latn.js" />
    <Content Include="Extensions\ckeditor\plugins\a11yhelp\dialogs\lang\sr.js" />
    <Content Include="Extensions\ckeditor\plugins\a11yhelp\dialogs\lang\sv.js" />
    <Content Include="Extensions\ckeditor\plugins\a11yhelp\dialogs\lang\th.js" />
    <Content Include="Extensions\ckeditor\plugins\a11yhelp\dialogs\lang\tr.js" />
    <Content Include="Extensions\ckeditor\plugins\a11yhelp\dialogs\lang\tt.js" />
    <Content Include="Extensions\ckeditor\plugins\a11yhelp\dialogs\lang\ug.js" />
    <Content Include="Extensions\ckeditor\plugins\a11yhelp\dialogs\lang\uk.js" />
    <Content Include="Extensions\ckeditor\plugins\a11yhelp\dialogs\lang\vi.js" />
    <Content Include="Extensions\ckeditor\plugins\a11yhelp\dialogs\lang\zh-cn.js" />
    <Content Include="Extensions\ckeditor\plugins\a11yhelp\dialogs\lang\zh.js" />
    <Content Include="Extensions\ckeditor\plugins\a11yhelp\dialogs\lang\_translationstatus.txt" />
    <Content Include="Extensions\ckeditor\plugins\about\dialogs\about.js" />
    <Content Include="Extensions\ckeditor\plugins\about\dialogs\hidpi\logo_ckeditor.png" />
    <Content Include="Extensions\ckeditor\plugins\about\dialogs\logo_ckeditor.png" />
    <Content Include="Extensions\ckeditor\plugins\colordialog\dialogs\colordialog.css" />
    <Content Include="Extensions\ckeditor\plugins\colordialog\dialogs\colordialog.js" />
    <Content Include="Extensions\ckeditor\plugins\copyformatting\cursors\cursor-disabled.svg" />
    <Content Include="Extensions\ckeditor\plugins\copyformatting\cursors\cursor.svg" />
    <Content Include="Extensions\ckeditor\plugins\copyformatting\styles\copyformatting.css" />
    <Content Include="Extensions\ckeditor\plugins\dialog\dialogDefinition.js" />
    <Content Include="Extensions\ckeditor\plugins\div\dialogs\div.js" />
    <Content Include="Extensions\ckeditor\plugins\find\dialogs\find.js" />
    <Content Include="Extensions\ckeditor\plugins\flash\dialogs\flash.js" />
    <Content Include="Extensions\ckeditor\plugins\flash\images\placeholder.png" />
    <Content Include="Extensions\ckeditor\plugins\forms\dialogs\button.js" />
    <Content Include="Extensions\ckeditor\plugins\forms\dialogs\checkbox.js" />
    <Content Include="Extensions\ckeditor\plugins\forms\dialogs\form.js" />
    <Content Include="Extensions\ckeditor\plugins\forms\dialogs\hiddenfield.js" />
    <Content Include="Extensions\ckeditor\plugins\forms\dialogs\radio.js" />
    <Content Include="Extensions\ckeditor\plugins\forms\dialogs\select.js" />
    <Content Include="Extensions\ckeditor\plugins\forms\dialogs\textarea.js" />
    <Content Include="Extensions\ckeditor\plugins\forms\dialogs\textfield.js" />
    <Content Include="Extensions\ckeditor\plugins\forms\images\hiddenfield.gif" />
    <Content Include="Extensions\ckeditor\plugins\icons.png" />
    <Content Include="Extensions\ckeditor\plugins\icons_hidpi.png" />
    <Content Include="Extensions\ckeditor\plugins\iframe\dialogs\iframe.js" />
    <Content Include="Extensions\ckeditor\plugins\iframe\images\placeholder.png" />
    <Content Include="Extensions\ckeditor\plugins\image\dialogs\image.js" />
    <Content Include="Extensions\ckeditor\plugins\image\images\noimage.png" />
    <Content Include="Extensions\ckeditor\plugins\link\dialogs\anchor.js" />
    <Content Include="Extensions\ckeditor\plugins\link\dialogs\link.js" />
    <Content Include="Extensions\ckeditor\plugins\link\images\anchor.png" />
    <Content Include="Extensions\ckeditor\plugins\link\images\hidpi\anchor.png" />
    <Content Include="Extensions\ckeditor\plugins\liststyle\dialogs\liststyle.js" />
    <Content Include="Extensions\ckeditor\plugins\magicline\images\hidpi\icon-rtl.png" />
    <Content Include="Extensions\ckeditor\plugins\magicline\images\hidpi\icon.png" />
    <Content Include="Extensions\ckeditor\plugins\magicline\images\icon-rtl.png" />
    <Content Include="Extensions\ckeditor\plugins\magicline\images\icon.png" />
    <Content Include="Extensions\ckeditor\plugins\pagebreak\images\pagebreak.gif" />
    <Content Include="Extensions\ckeditor\plugins\pastefromword\filter\default.js" />
    <Content Include="Extensions\ckeditor\plugins\preview\preview.html" />
    <Content Include="Extensions\ckeditor\plugins\scayt\dialogs\dialog.css" />
    <Content Include="Extensions\ckeditor\plugins\scayt\dialogs\options.js" />
    <Content Include="Extensions\ckeditor\plugins\scayt\dialogs\toolbar.css" />
    <Content Include="Extensions\ckeditor\plugins\scayt\skins\moono-lisa\scayt.css" />
    <Content Include="Extensions\ckeditor\plugins\showblocks\images\block_address.png" />
    <Content Include="Extensions\ckeditor\plugins\showblocks\images\block_blockquote.png" />
    <Content Include="Extensions\ckeditor\plugins\showblocks\images\block_div.png" />
    <Content Include="Extensions\ckeditor\plugins\showblocks\images\block_h1.png" />
    <Content Include="Extensions\ckeditor\plugins\showblocks\images\block_h2.png" />
    <Content Include="Extensions\ckeditor\plugins\showblocks\images\block_h3.png" />
    <Content Include="Extensions\ckeditor\plugins\showblocks\images\block_h4.png" />
    <Content Include="Extensions\ckeditor\plugins\showblocks\images\block_h5.png" />
    <Content Include="Extensions\ckeditor\plugins\showblocks\images\block_h6.png" />
    <Content Include="Extensions\ckeditor\plugins\showblocks\images\block_p.png" />
    <Content Include="Extensions\ckeditor\plugins\showblocks\images\block_pre.png" />
    <Content Include="Extensions\ckeditor\plugins\smiley\dialogs\smiley.js" />
    <Content Include="Extensions\ckeditor\plugins\smiley\images\angel_smile.gif" />
    <Content Include="Extensions\ckeditor\plugins\smiley\images\angel_smile.png" />
    <Content Include="Extensions\ckeditor\plugins\smiley\images\angry_smile.gif" />
    <Content Include="Extensions\ckeditor\plugins\smiley\images\angry_smile.png" />
    <Content Include="Extensions\ckeditor\plugins\smiley\images\broken_heart.gif" />
    <Content Include="Extensions\ckeditor\plugins\smiley\images\broken_heart.png" />
    <Content Include="Extensions\ckeditor\plugins\smiley\images\confused_smile.gif" />
    <Content Include="Extensions\ckeditor\plugins\smiley\images\confused_smile.png" />
    <Content Include="Extensions\ckeditor\plugins\smiley\images\cry_smile.gif" />
    <Content Include="Extensions\ckeditor\plugins\smiley\images\cry_smile.png" />
    <Content Include="Extensions\ckeditor\plugins\smiley\images\devil_smile.gif" />
    <Content Include="Extensions\ckeditor\plugins\smiley\images\devil_smile.png" />
    <Content Include="Extensions\ckeditor\plugins\smiley\images\embaressed_smile.gif" />
    <Content Include="Extensions\ckeditor\plugins\smiley\images\embarrassed_smile.gif" />
    <Content Include="Extensions\ckeditor\plugins\smiley\images\embarrassed_smile.png" />
    <Content Include="Extensions\ckeditor\plugins\smiley\images\envelope.gif" />
    <Content Include="Extensions\ckeditor\plugins\smiley\images\envelope.png" />
    <Content Include="Extensions\ckeditor\plugins\smiley\images\heart.gif" />
    <Content Include="Extensions\ckeditor\plugins\smiley\images\heart.png" />
    <Content Include="Extensions\ckeditor\plugins\smiley\images\kiss.gif" />
    <Content Include="Extensions\ckeditor\plugins\smiley\images\kiss.png" />
    <Content Include="Extensions\ckeditor\plugins\smiley\images\lightbulb.gif" />
    <Content Include="Extensions\ckeditor\plugins\smiley\images\lightbulb.png" />
    <Content Include="Extensions\ckeditor\plugins\smiley\images\omg_smile.gif" />
    <Content Include="Extensions\ckeditor\plugins\smiley\images\omg_smile.png" />
    <Content Include="Extensions\ckeditor\plugins\smiley\images\regular_smile.gif" />
    <Content Include="Extensions\ckeditor\plugins\smiley\images\regular_smile.png" />
    <Content Include="Extensions\ckeditor\plugins\smiley\images\sad_smile.gif" />
    <Content Include="Extensions\ckeditor\plugins\smiley\images\sad_smile.png" />
    <Content Include="Extensions\ckeditor\plugins\smiley\images\shades_smile.gif" />
    <Content Include="Extensions\ckeditor\plugins\smiley\images\shades_smile.png" />
    <Content Include="Extensions\ckeditor\plugins\smiley\images\teeth_smile.gif" />
    <Content Include="Extensions\ckeditor\plugins\smiley\images\teeth_smile.png" />
    <Content Include="Extensions\ckeditor\plugins\smiley\images\thumbs_down.gif" />
    <Content Include="Extensions\ckeditor\plugins\smiley\images\thumbs_down.png" />
    <Content Include="Extensions\ckeditor\plugins\smiley\images\thumbs_up.gif" />
    <Content Include="Extensions\ckeditor\plugins\smiley\images\thumbs_up.png" />
    <Content Include="Extensions\ckeditor\plugins\smiley\images\tongue_smile.gif" />
    <Content Include="Extensions\ckeditor\plugins\smiley\images\tongue_smile.png" />
    <Content Include="Extensions\ckeditor\plugins\smiley\images\tounge_smile.gif" />
    <Content Include="Extensions\ckeditor\plugins\smiley\images\whatchutalkingabout_smile.gif" />
    <Content Include="Extensions\ckeditor\plugins\smiley\images\whatchutalkingabout_smile.png" />
    <Content Include="Extensions\ckeditor\plugins\smiley\images\wink_smile.gif" />
    <Content Include="Extensions\ckeditor\plugins\smiley\images\wink_smile.png" />
    <Content Include="Extensions\ckeditor\plugins\specialchar\dialogs\lang\af.js" />
    <Content Include="Extensions\ckeditor\plugins\specialchar\dialogs\lang\ar.js" />
    <Content Include="Extensions\ckeditor\plugins\specialchar\dialogs\lang\az.js" />
    <Content Include="Extensions\ckeditor\plugins\specialchar\dialogs\lang\bg.js" />
    <Content Include="Extensions\ckeditor\plugins\specialchar\dialogs\lang\ca.js" />
    <Content Include="Extensions\ckeditor\plugins\specialchar\dialogs\lang\cs.js" />
    <Content Include="Extensions\ckeditor\plugins\specialchar\dialogs\lang\cy.js" />
    <Content Include="Extensions\ckeditor\plugins\specialchar\dialogs\lang\da.js" />
    <Content Include="Extensions\ckeditor\plugins\specialchar\dialogs\lang\de-ch.js" />
    <Content Include="Extensions\ckeditor\plugins\specialchar\dialogs\lang\de.js" />
    <Content Include="Extensions\ckeditor\plugins\specialchar\dialogs\lang\el.js" />
    <Content Include="Extensions\ckeditor\plugins\specialchar\dialogs\lang\en-au.js" />
    <Content Include="Extensions\ckeditor\plugins\specialchar\dialogs\lang\en-ca.js" />
    <Content Include="Extensions\ckeditor\plugins\specialchar\dialogs\lang\en-gb.js" />
    <Content Include="Extensions\ckeditor\plugins\specialchar\dialogs\lang\en.js" />
    <Content Include="Extensions\ckeditor\plugins\specialchar\dialogs\lang\eo.js" />
    <Content Include="Extensions\ckeditor\plugins\specialchar\dialogs\lang\es-mx.js" />
    <Content Include="Extensions\ckeditor\plugins\specialchar\dialogs\lang\es.js" />
    <Content Include="Extensions\ckeditor\plugins\specialchar\dialogs\lang\et.js" />
    <Content Include="Extensions\ckeditor\plugins\specialchar\dialogs\lang\eu.js" />
    <Content Include="Extensions\ckeditor\plugins\specialchar\dialogs\lang\fa.js" />
    <Content Include="Extensions\ckeditor\plugins\specialchar\dialogs\lang\fi.js" />
    <Content Include="Extensions\ckeditor\plugins\specialchar\dialogs\lang\fr-ca.js" />
    <Content Include="Extensions\ckeditor\plugins\specialchar\dialogs\lang\fr.js" />
    <Content Include="Extensions\ckeditor\plugins\specialchar\dialogs\lang\gl.js" />
    <Content Include="Extensions\ckeditor\plugins\specialchar\dialogs\lang\he.js" />
    <Content Include="Extensions\ckeditor\plugins\specialchar\dialogs\lang\hr.js" />
    <Content Include="Extensions\ckeditor\plugins\specialchar\dialogs\lang\hu.js" />
    <Content Include="Extensions\ckeditor\plugins\specialchar\dialogs\lang\id.js" />
    <Content Include="Extensions\ckeditor\plugins\specialchar\dialogs\lang\it.js" />
    <Content Include="Extensions\ckeditor\plugins\specialchar\dialogs\lang\ja.js" />
    <Content Include="Extensions\ckeditor\plugins\specialchar\dialogs\lang\km.js" />
    <Content Include="Extensions\ckeditor\plugins\specialchar\dialogs\lang\ko.js" />
    <Content Include="Extensions\ckeditor\plugins\specialchar\dialogs\lang\ku.js" />
    <Content Include="Extensions\ckeditor\plugins\specialchar\dialogs\lang\lt.js" />
    <Content Include="Extensions\ckeditor\plugins\specialchar\dialogs\lang\lv.js" />
    <Content Include="Extensions\ckeditor\plugins\specialchar\dialogs\lang\nb.js" />
    <Content Include="Extensions\ckeditor\plugins\specialchar\dialogs\lang\nl.js" />
    <Content Include="Extensions\ckeditor\plugins\specialchar\dialogs\lang\no.js" />
    <Content Include="Extensions\ckeditor\plugins\specialchar\dialogs\lang\oc.js" />
    <Content Include="Extensions\ckeditor\plugins\specialchar\dialogs\lang\pl.js" />
    <Content Include="Extensions\ckeditor\plugins\specialchar\dialogs\lang\pt-br.js" />
    <Content Include="Extensions\ckeditor\plugins\specialchar\dialogs\lang\pt.js" />
    <Content Include="Extensions\ckeditor\plugins\specialchar\dialogs\lang\ru.js" />
    <Content Include="Extensions\ckeditor\plugins\specialchar\dialogs\lang\si.js" />
    <Content Include="Extensions\ckeditor\plugins\specialchar\dialogs\lang\sk.js" />
    <Content Include="Extensions\ckeditor\plugins\specialchar\dialogs\lang\sl.js" />
    <Content Include="Extensions\ckeditor\plugins\specialchar\dialogs\lang\sq.js" />
    <Content Include="Extensions\ckeditor\plugins\specialchar\dialogs\lang\sv.js" />
    <Content Include="Extensions\ckeditor\plugins\specialchar\dialogs\lang\th.js" />
    <Content Include="Extensions\ckeditor\plugins\specialchar\dialogs\lang\tr.js" />
    <Content Include="Extensions\ckeditor\plugins\specialchar\dialogs\lang\tt.js" />
    <Content Include="Extensions\ckeditor\plugins\specialchar\dialogs\lang\ug.js" />
    <Content Include="Extensions\ckeditor\plugins\specialchar\dialogs\lang\uk.js" />
    <Content Include="Extensions\ckeditor\plugins\specialchar\dialogs\lang\vi.js" />
    <Content Include="Extensions\ckeditor\plugins\specialchar\dialogs\lang\zh-cn.js" />
    <Content Include="Extensions\ckeditor\plugins\specialchar\dialogs\lang\zh.js" />
    <Content Include="Extensions\ckeditor\plugins\specialchar\dialogs\lang\_translationstatus.txt" />
    <Content Include="Extensions\ckeditor\plugins\specialchar\dialogs\specialchar.js" />
    <Content Include="Extensions\ckeditor\plugins\tableselection\styles\tableselection.css" />
    <Content Include="Extensions\ckeditor\plugins\tabletools\dialogs\tableCell.js" />
    <Content Include="Extensions\ckeditor\plugins\table\dialogs\table.js" />
    <Content Include="Extensions\ckeditor\plugins\templates\dialogs\templates.css" />
    <Content Include="Extensions\ckeditor\plugins\templates\dialogs\templates.js" />
    <Content Include="Extensions\ckeditor\plugins\templates\templates\default.js" />
    <Content Include="Extensions\ckeditor\plugins\templates\templates\images\template1.gif" />
    <Content Include="Extensions\ckeditor\plugins\templates\templates\images\template2.gif" />
    <Content Include="Extensions\ckeditor\plugins\templates\templates\images\template3.gif" />
    <Content Include="Extensions\ckeditor\plugins\wsc\dialogs\ciframe.html" />
    <Content Include="Extensions\ckeditor\plugins\wsc\dialogs\tmpFrameset.html" />
    <Content Include="Extensions\ckeditor\plugins\wsc\dialogs\wsc.css" />
    <Content Include="Extensions\ckeditor\plugins\wsc\dialogs\wsc.js" />
    <Content Include="Extensions\ckeditor\plugins\wsc\dialogs\wsc_ie.js" />
    <Content Include="Extensions\ckeditor\plugins\wsc\skins\moono-lisa\wsc.css" />
    <Content Include="Extensions\ckeditor\samples\css\samples.css" />
    <Content Include="Extensions\ckeditor\samples\img\github-top.png" />
    <Content Include="Extensions\ckeditor\samples\img\header-bg.png" />
    <Content Include="Extensions\ckeditor\samples\img\header-separator.png" />
    <Content Include="Extensions\ckeditor\samples\img\logo.png" />
    <Content Include="Extensions\ckeditor\samples\img\navigation-tip.png" />
    <Content Include="Extensions\ckeditor\samples\index.html" />
    <Content Include="Extensions\ckeditor\samples\js\sample.js" />
    <Content Include="Extensions\ckeditor\samples\js\sf.js" />
    <Content Include="Extensions\ckeditor\samples\old\ajax.html" />
    <Content Include="Extensions\ckeditor\samples\old\api.html" />
    <Content Include="Extensions\ckeditor\samples\old\appendto.html" />
    <Content Include="Extensions\ckeditor\samples\old\assets\inlineall\logo.png" />
    <Content Include="Extensions\ckeditor\samples\old\assets\outputxhtml\outputxhtml.css" />
    <Content Include="Extensions\ckeditor\samples\old\assets\posteddata.php" />
    <Content Include="Extensions\ckeditor\samples\old\assets\sample.jpg" />
    <Content Include="Extensions\ckeditor\samples\old\assets\uilanguages\languages.js" />
    <Content Include="Extensions\ckeditor\samples\old\datafiltering.html" />
    <Content Include="Extensions\ckeditor\samples\old\dialog\assets\my_dialog.js" />
    <Content Include="Extensions\ckeditor\samples\old\dialog\dialog.html" />
    <Content Include="Extensions\ckeditor\samples\old\divreplace.html" />
    <Content Include="Extensions\ckeditor\samples\old\enterkey\enterkey.html" />
    <Content Include="Extensions\ckeditor\samples\old\htmlwriter\assets\outputforflash\outputforflash.swf" />
    <Content Include="Extensions\ckeditor\samples\old\htmlwriter\assets\outputforflash\swfobject.js" />
    <Content Include="Extensions\ckeditor\samples\old\htmlwriter\outputforflash.html" />
    <Content Include="Extensions\ckeditor\samples\old\htmlwriter\outputhtml.html" />
    <Content Include="Extensions\ckeditor\samples\old\index.html" />
    <Content Include="Extensions\ckeditor\samples\old\inlineall.html" />
    <Content Include="Extensions\ckeditor\samples\old\inlinebycode.html" />
    <Content Include="Extensions\ckeditor\samples\old\inlinetextarea.html" />
    <Content Include="Extensions\ckeditor\samples\old\jquery.html" />
    <Content Include="Extensions\ckeditor\samples\old\magicline\magicline.html" />
    <Content Include="Extensions\ckeditor\samples\old\readonly.html" />
    <Content Include="Extensions\ckeditor\samples\old\replacebyclass.html" />
    <Content Include="Extensions\ckeditor\samples\old\replacebycode.html" />
    <Content Include="Extensions\ckeditor\samples\old\sample.css" />
    <Content Include="Extensions\ckeditor\samples\old\sample.js" />
    <Content Include="Extensions\ckeditor\samples\old\sample_posteddata.php" />
    <Content Include="Extensions\ckeditor\samples\old\tabindex.html" />
    <Content Include="Extensions\ckeditor\samples\old\toolbar\toolbar.html" />
    <Content Include="Extensions\ckeditor\samples\old\uicolor.html" />
    <Content Include="Extensions\ckeditor\samples\old\uilanguages.html" />
    <Content Include="Extensions\ckeditor\samples\old\wysiwygarea\fullpage.html" />
    <Content Include="Extensions\ckeditor\samples\old\xhtmlstyle.html" />
    <Content Include="Extensions\ckeditor\samples\toolbarconfigurator\css\fontello.css" />
    <Content Include="Extensions\ckeditor\samples\toolbarconfigurator\font\fontello.svg" />
    <Content Include="Extensions\ckeditor\samples\toolbarconfigurator\font\LICENSE.txt" />
    <Content Include="Extensions\ckeditor\samples\toolbarconfigurator\index.html" />
    <Content Include="Extensions\ckeditor\samples\toolbarconfigurator\js\abstracttoolbarmodifier.js" />
    <Content Include="Extensions\ckeditor\samples\toolbarconfigurator\js\fulltoolbareditor.js" />
    <Content Include="Extensions\ckeditor\samples\toolbarconfigurator\js\toolbarmodifier.js" />
    <Content Include="Extensions\ckeditor\samples\toolbarconfigurator\js\toolbartextmodifier.js" />
    <Content Include="Extensions\ckeditor\samples\toolbarconfigurator\lib\codemirror\codemirror.css" />
    <Content Include="Extensions\ckeditor\samples\toolbarconfigurator\lib\codemirror\codemirror.js" />
    <Content Include="Extensions\ckeditor\samples\toolbarconfigurator\lib\codemirror\javascript.js" />
    <Content Include="Extensions\ckeditor\samples\toolbarconfigurator\lib\codemirror\neo.css" />
    <Content Include="Extensions\ckeditor\samples\toolbarconfigurator\lib\codemirror\show-hint.css" />
    <Content Include="Extensions\ckeditor\samples\toolbarconfigurator\lib\codemirror\show-hint.js" />
    <Content Include="Extensions\ckeditor\skins\moono-lisa\dialog.css" />
    <Content Include="Extensions\ckeditor\skins\moono-lisa\dialog_ie.css" />
    <Content Include="Extensions\ckeditor\skins\moono-lisa\dialog_ie8.css" />
    <Content Include="Extensions\ckeditor\skins\moono-lisa\dialog_iequirks.css" />
    <Content Include="Extensions\ckeditor\skins\moono-lisa\editor.css" />
    <Content Include="Extensions\ckeditor\skins\moono-lisa\editor_gecko.css" />
    <Content Include="Extensions\ckeditor\skins\moono-lisa\editor_ie.css" />
    <Content Include="Extensions\ckeditor\skins\moono-lisa\editor_ie8.css" />
    <Content Include="Extensions\ckeditor\skins\moono-lisa\editor_iequirks.css" />
    <Content Include="Extensions\ckeditor\skins\moono-lisa\icons.png" />
    <Content Include="Extensions\ckeditor\skins\moono-lisa\icons_hidpi.png" />
    <Content Include="Extensions\ckeditor\skins\moono-lisa\images\arrow.png" />
    <Content Include="Extensions\ckeditor\skins\moono-lisa\images\close.png" />
    <Content Include="Extensions\ckeditor\skins\moono-lisa\images\hidpi\close.png" />
    <Content Include="Extensions\ckeditor\skins\moono-lisa\images\hidpi\lock-open.png" />
    <Content Include="Extensions\ckeditor\skins\moono-lisa\images\hidpi\lock.png" />
    <Content Include="Extensions\ckeditor\skins\moono-lisa\images\hidpi\refresh.png" />
    <Content Include="Extensions\ckeditor\skins\moono-lisa\images\lock-open.png" />
    <Content Include="Extensions\ckeditor\skins\moono-lisa\images\lock.png" />
    <Content Include="Extensions\ckeditor\skins\moono-lisa\images\refresh.png" />
    <Content Include="Extensions\ckeditor\skins\moono-lisa\images\spinner.gif" />
    <Content Include="Extensions\ckeditor\styles.js" />
    <Content Include="favicon.ico" />
    <Content Include="images\bookingDHL.png" />
    <Content Include="images\bookingfake.jpg" />
    <Content Include="images\CARGO_READY.png" />
    <Content Include="images\imageCapNhatHangThucTe.PNG" />
    <Content Include="images\imagesAWB\AccountingInformation.gif" />
    <Content Include="images\imagesAWB\AccountNo.gif" />
    <Content Include="images\imagesAWB\AgentIATACode.gif" />
    <Content Include="images\imagesAWB\AgentSignature.gif" />
    <Content Include="images\imagesAWB\AirportOfDepature.gif" />
    <Content Include="images\imagesAWB\AirportOfDestination.gif" />
    <Content Include="images\imagesAWB\AirWayBill.gif" />
    <Content Include="images\imagesAWB\AmountOfInsurance.gif" />
    <Content Include="images\imagesAWB\At.gif" />
    <Content Include="images\imagesAWB\banner.jpg" />
    <Content Include="images\imagesAWB\Barcode.gif" />
    <Content Include="images\imagesAWB\By.gif" />
    <Content Include="images\imagesAWB\Carrier.gif" />
    <Content Include="images\imagesAWB\CarrierUseOnly.gif" />
    <Content Include="images\imagesAWB\ChargableWeight.gif" />
    <Content Include="images\imagesAWB\ChargesAtDestination.gif" />
    <Content Include="images\imagesAWB\ChargesInDestinationCurrency.gif" />
    <Content Include="images\imagesAWB\ChgsCode.gif" />
    <Content Include="images\imagesAWB\Coll.gif" />
    <Content Include="images\imagesAWB\Commodity.gif" />
    <Content Include="images\imagesAWB\ConsigneeAccountNo.gif" />
    <Content Include="images\imagesAWB\ConsigneeName.gif" />
    <Content Include="images\imagesAWB\Copies.gif" />
    <Content Include="images\imagesAWB\Currency.gif" />
    <Content Include="images\imagesAWB\CurrencyConversionRate.gif" />
    <Content Include="images\imagesAWB\Date.gif" />
    <Content Include="images\imagesAWB\Declaration.gif" />
    <Content Include="images\imagesAWB\DeclaredValueForCarriage.gif" />
    <Content Include="images\imagesAWB\DeclaredValueForCustoms.gif" />
    <Content Include="images\imagesAWB\Description.gif" />
    <Content Include="images\imagesAWB\ExecutedOn.gif" />
    <Content Include="images\imagesAWB\FirstCarrier.gif" />
    <Content Include="images\imagesAWB\func_hdr_left.gif" />
    <Content Include="images\imagesAWB\func_hdr_right.gif" />
    <Content Include="images\imagesAWB\GrossWeight.gif" />
    <Content Include="images\imagesAWB\HandlingInformation.gif" />
    <Content Include="images\imagesAWB\HM_More_black_right.gif" />
    <Content Include="images\imagesAWB\image005.png" />
    <Content Include="images\imagesAWB\image008.png" />
    <Content Include="images\imagesAWB\inbox3.gif" />
    <Content Include="images\imagesAWB\Insurance.gif" />
    <Content Include="images\imagesAWB\IssuingCarrier.gif" />
    <Content Include="images\imagesAWB\KgLb.gif" />
    <Content Include="images\imagesAWB\logo_glshk.jpg" />
    <Content Include="images\imagesAWB\miniaturebulb.gif" />
    <Content Include="images\imagesAWB\OSI.gif" />
    <Content Include="images\imagesAWB\Other.gif" />
    <Content Include="images\imagesAWB\OtherChargesName.gif" />
    <Content Include="images\imagesAWB\Place.gif" />
    <Content Include="images\imagesAWB\PPD.gif" />
    <Content Include="images\imagesAWB\Rate.gif" />
    <Content Include="images\imagesAWB\RateClass.gif" />
    <Content Include="images\imagesAWB\RCP.gif" />
    <Content Include="images\imagesAWB\rollprev.gif" />
    <Content Include="images\imagesAWB\SCI.gif" />
    <Content Include="images\imagesAWB\ShipperAccountNo.gif" />
    <Content Include="images\imagesAWB\ShipperCertify.gif" />
    <Content Include="images\imagesAWB\ShipperName.gif" />
    <Content Include="images\imagesAWB\ShipperSignature.gif" />
    <Content Include="images\imagesAWB\spacer.gif" />
    <Content Include="images\imagesAWB\Tax.gif" />
    <Content Include="images\imagesAWB\To.gif" />
    <Content Include="images\imagesAWB\Total.gif" />
    <Content Include="images\imagesAWB\TotalCollect.gif" />
    <Content Include="images\imagesAWB\TotalCollectCharge.gif" />
    <Content Include="images\imagesAWB\TotalOtherChargesDueAgent.gif" />
    <Content Include="images\imagesAWB\TotalOtherChargesDueCarrier.gif" />
    <Content Include="images\imagesAWB\TotalPrepaid.gif" />
    <Content Include="images\imagesAWB\ValuationCharge.gif" />
    <Content Include="images\imagesAWB\WeightCharge.gif" />
    <Content Include="images\imagesAWB\WtVal.gif" />
    <Content Include="images\imageStorage.PNG" />
    <Content Include="images\imageSuakehoach.PNG" />
    <Content Include="images\imageSuaSR.PNG" />
    <Content Include="images\imagesUNI\Cathay.png" />
    <Content Include="images\imagesUNI\CHINA.JPG" />
    <Content Include="images\imagesUNI\China2.jpg" />
    <Content Include="images\imagesUNI\EB.png" />
    <Content Include="images\imagesUNI\Emirest.png" />
    <Content Include="images\imagesUNI\ethiopia.png" />
    <Content Include="images\imagesUNI\ETIHAD.png" />
    <Content Include="images\imagesUNI\EVA.jpg" />
    <Content Include="images\imagesUNI\IATA.png" />
    <Content Include="images\imagesUNI\Korean.png" />
    <Content Include="images\imagesUNI\UNIQUE.jpg" />
    <Content Include="images\khoAa2.jpg" />
    <Content Include="images\khoB2.jpg" />
    <Content Include="images\OPS\Airline\image008.png" />
    <Content Include="images\OPS\Airline\image165.png" />
    <Content Include="images\OPS\back-image.jpg" />
    <Content Include="images\OPS\background-black.png" />
    <Content Include="images\OPS\barcode1.png" />
    <Content Include="images\OPS\barcode2.png" />
    <Content Include="images\OPS\DHL.png" />
    <Content Include="images\OPS\hinh-anh-mau-den.png" />
    <Content Include="images\OPS\logo.png" />
    <Content Include="inlabel.aspx" />
    <Content Include="InputExcel2.aspx" />
    <Content Include="InputExcelBook.aspx" />
    <Content Include="InputExcelBookVsip.aspx" />
    <Content Include="IP.aspx" />
    <Content Include="js\bootstrap-tagsinput.min.js" />
    <Content Include="js\custom\BangChamCong.js" />
    <Content Include="js\custom\BangChamCongLoi.js" />
    <Content Include="js\custom\BaoCaoOther.js" />
    <Content Include="js\custom\BilldingvsUpdate.js" />
    <Content Include="js\custom\ChamCong-BangTin.js" />
    <Content Include="js\custom\ChamCong-TrangThai.js" />
    <Content Include="js\custom\ChamCong-XuatBangChamCong.js" />
    <Content Include="js\custom\ChuyenPhatNhanhCPN.js" />
    <Content Include="js\custom\ChuyenPhatNhanh_V2.js" />
    <Content Include="js\custom\CongViec-BangTin.js" />
    <Content Include="js\custom\CPNView.js" />
    <Content Include="js\custom\DangKyVaoRa.js" />
    <Content Include="js\custom\DanhSachDNN.js" />
    <Content Include="js\custom\DanhSachVatTu.js" />
    <Content Include="js\custom\DeNghiCapPhat.js" />
    <Content Include="js\custom\deploy.js" />
    <Content Include="js\custom\Distribution-Customers.js" />
    <Content Include="js\custom\Distribution-DeliveryOrders.js" />
    <Content Include="js\custom\Distribution-Products.js" />
    <Content Include="js\custom\Distribution-Route.js" />
    <Content Include="js\custom\GetZalo.js" />
    <Content Include="js\custom\inlabel.js" />
    <Content Include="js\custom\input-excel2.js" />
    <Content Include="js\custom\InputExcelBook.js" />
    <Content Include="js\custom\InputExcelBookVsip.js" />
    <Content Include="js\custom\KeHoachDetails.js" />
    <Content Include="js\custom\KeHoachLayHang.js" />
    <Content Include="js\custom\KhoVsip1.js" />
    <Content Include="js\custom\KhoVsip2.js" />
    <Content Include="js\custom\OPS.js" />
    <Content Include="js\custom\PODView.js" />
    <Content Include="js\custom\QRCode.js" />
    <Content Include="js\custom\quan-ly-khvc2.js" />
    <Content Include="js\custom\QuanLyAPPLEKeHoach.js" />
    <Content Include="js\custom\QuanLyAPPLEXuatKho.js" />
    <Content Include="js\custom\QuanLyChamCong-DiMuon.js" />
    <Content Include="js\custom\QuanLyChiHo.js" />
    <Content Include="js\custom\QuanLyChiHoNCC.js" />
    <Content Include="js\custom\QuanLyChuyenXeCPN.js" />
    <Content Include="js\custom\QuanLyCongVanHQ.js" />
    <Content Include="js\custom\QuanLyGiaoNhanDOC.js" />
    <Content Include="js\custom\QuanLyHangNhap.js" />
    <Content Include="js\custom\QuanLyHangNhapKhachHang.js" />
    <Content Include="js\custom\QuanLyHangXuat.js" />
    <Content Include="js\custom\QuanLyHangXuatASG.js" />
    <Content Include="js\custom\QuanLyHangXuatKhachHang.js" />
    <Content Include="js\custom\QuanLyInTem.js" />
    <Content Include="js\custom\QuanLyKhoFM.js" />
    <Content Include="js\custom\QuanLyKhoNhap.js" />
    <Content Include="js\custom\QuanLyLoi.js" />
    <Content Include="js\custom\QuanLyNhanVien.js" />
    <Content Include="js\custom\QuanLyNhapVatTu.js" />
    <Content Include="js\custom\QuanLyPOD.js" />
    <Content Include="js\custom\QuanLyPODView.js" />
    <Content Include="js\custom\QuanLySuatAn.js" />
    <Content Include="js\custom\QuanLyViTriKhoMoi.js" />
    <Content Include="js\custom\testPrint.js" />
    <Content Include="js\custom\thanhtoan-baocao.js" />
    <Content Include="js\custom\thanhtoan-khachhangdoitac.js" />
    <Content Include="js\custom\TraCuuDienEDI.js" />
    <Content Include="js\custom\TraCuuDNN.js" />
    <Content Include="js\custom\trang-thai-hang-nhap.js" />
    <Content Include="js\custom\TrienKhaiVanBan.js" />
    <Content Include="js\custom\TruckGiaBan.js" />
    <Content Include="js\custom\TruckGiaMua.js" />
    <Content Include="js\custom\TruckKhachHang.js" />
    <Content Include="js\custom\TruckTraCuu.js" />
    <Content Include="js\custom\TruckTuyen.js" />
    <Content Include="js\custom\TruyVanCPN.js" />
    <Content Include="js\custom\TruyVanDNN.js" />
    <Content Include="js\custom\TruyVanPo.js" />
    <Content Include="js\custom\UpdateFWD.js" />
    <Content Include="js\custom\XemAnhHoaDonXuat.js" />
    <Content Include="js\custom\XemCanDIM.js" />
    <Content Include="js\dataTables.fixedColumns.min.js" />
    <Content Include="js\extensions\qrcode.js" />
    <Content Include="js\extensions\qrcode.min.js" />
    <Content Include="js\notify.js" />
    <Content Include="js\notify.min.js" />
    <Content Include="js\jquery-barcode.js" />
    <Content Include="js\jquery-barcode.min.js" />
    <Content Include="js\JsBarcode.all.min.js" />
    <Content Include="js\JsBarcode.all.min.js.js" />
    <Content Include="js\print.min.js" />
    <Content Include="js\select2.min.js" />
    <Content Include="js\typeahead.bundle.min.js" />
    <Content Include="Distribution\Customers.aspx" />
    <Content Include="Distribution\Products.aspx" />
    <Content Include="KeHoachDetails.aspx" />
    <Content Include="KhaiBaoYTe\css\Default.css" />
    <Content Include="KhaiBaoYTe\css\QuanLyKhaiBao.css" />
    <Content Include="KhaiBaoYTe\Default.aspx" />
    <Content Include="KhaiBaoYTe\js\Default.js" />
    <Content Include="KhaiBaoYTe\js\Master.js" />
    <Content Include="KhaiBaoYTe\js\QuanLyKhaiBao.js" />
    <Content Include="KhaiBaoYTe\KhaiBao.aspx" />
    <Content Include="KhaiBaoYTe\QuanLyKhaiBao.aspx" />
    <Content Include="KhoVsip1.aspx" />
    <Content Include="KhoVsip2.aspx" />
    <Content Include="OPS.aspx" />
    <Content Include="PdfViewer.aspx" />
    <Content Include="PODView.aspx" />
    <Content Include="QRCode.aspx" />
    <Content Include="QuanLyAPPLEKeHoach.aspx" />
    <Content Include="QuanLyAPPLEXuatKho.aspx" />
    <Content Include="QuanLyBaoCaoNgay.aspx" />
    <Content Include="css\bootstrap-markdown.min.css" />
    <Content Include="css\custom\QuanLyBaoCaoNgay.css" />
    <Content Include="css\custom\dgr.css" />
    <Content Include="css\custom\export.css" />
    <Content Include="css\custom\import-status.css" />
    <Content Include="css\custom\input-excel.css" />
    <Content Include="css\custom\quan-ly-khvc.css" />
    <Content Include="css\custom\QuanLyLogistics.css" />
    <Content Include="css\custom\QuanLyNhomTaiKhoan.css" />
    <Content Include="css\custom\report-quan-ly-kho-thuong.css" />
    <Content Include="css\custom\thanhtoan-thanhtoan.css" />
    <Content Include="css\custom\trang-thai-hang-xuat.css" />
    <Content Include="css\sweetalert2.min.css" />
    <Content Include="js\custom\QuanLyBaoCaoNgay.js" />
    <Content Include="js\custom\QuanLyLogistics.js" />
    <Content Include="js\custom\QuanLyNhomTaiKhoan.js" />
    <Content Include="js\custom\QuanLyTaiKhoan.js" />
    <Content Include="js\extensions\jszip.min.js" />
    <Content Include="css\handsontable.full.min.css" />
    <Content Include="css\tooltipster\theme\tooltipster-sideTip-borderless.min.css" />
    <Content Include="css\tooltipster\theme\tooltipster-sideTip-light.min.css" />
    <Content Include="css\tooltipster\theme\tooltipster-sideTip-noir.min.css" />
    <Content Include="css\tooltipster\theme\tooltipster-sideTip-punk.min.css" />
    <Content Include="css\tooltipster\theme\tooltipster-sideTip-shadow.min.css" />
    <Content Include="css\tooltipster\tooltipster.bundle.min.css" />
    <Content Include="DataRoot\js\jquery.webticker.min.js" />
    <Content Include="DGR.aspx" />
    <Content Include="DownloadFile.aspx" />
    <Content Include="Email_Temp\HandlingDGRMNFTemp.html" />
    <Content Include="Email_Temp\HandlingDGRMNFTrCtnsTemp.html" />
    <Content Include="Email_Temp\HandlingDGRMNFTrNetWeightCtnsTemp.html" />
    <Content Include="Email_Temp\HandlingDGRMNFTrNetWeightPltsTemp.html" />
    <Content Include="Email_Temp\HandlingDGRMNFTrOverpackTemp.html" />
    <Content Include="Email_Temp\HandlingDGRMNFTrPalletTemp.html" />
    <Content Include="Email_Temp\HandlingDGRMNFTrTemp.html" />
    <Content Include="Export.aspx" />
    <Content Include="images\export\airport.png" />
    <Content Include="images\export\accept.png" />
    <Content Include="images\export\complete.png" />
    <Content Include="images\export\truckingtonba.png" />
    <Content Include="images\export\preaccept.png" />
    <Content Include="images\export\loadingontruck.png" />
    <Content Include="images\loading.gif" />
    <Content Include="images\plan.png" />
    <Content Include="images\export\booking.png" />
    <Content Include="images\poi.gif" />
    <Content Include="images\radio.gif" />
    <Content Include="images\squares.gif" />
    <Content Include="ImportStatus.aspx" />
    <Content Include="InputExcel.aspx" />
    <Content Include="js\bootstrap-markdown.js" />
    <Content Include="js\custom\dgr.js" />
    <Content Include="js\custom\export.js" />
    <Content Include="js\custom\gen-table.js" />
    <Content Include="js\custom\import-status.js" />
    <Content Include="js\custom\input-excel.js" />
    <Content Include="js\custom\quan-ly-khvc.js" />
    <Content Include="js\custom\report-quan-ly-kho-thuong.js" />
    <Content Include="js\custom\thanhtoan-thanhtoan.js" />
    <Content Include="js\custom\trang-thai-hang-xuat.js" />
    <Content Include="js\extensions\kendo.all.min.js" />
    <Content Include="js\handsontable.full.min.js" />
    <Content Include="js\jquery.min.js" />
    <Content Include="js\jQuery.print.js" />
    <Content Include="js\linq.min.js" />
    <Content Include="js\marked.js" />
    <Content Include="js\moment.js" />
    <Content Include="js\sweetalert2.min.js" />
    <Content Include="js\tooltipster.bundle.min.js" />
    <Content Include="KiemSoatHangSoiChieu.aspx" />
    <Content Include="Admin\QuanLyTaiKhoan.aspx" />
    <Content Include="ChamCong\BangChamCong.aspx" />
    <Content Include="BangTongHopDVASG.aspx" />
    <Content Include="BaoCaoXuatKho.aspx" />
    <Content Include="BaoCaoTongHop.aspx" />
    <Content Include="ChamCong\TrangThai.aspx" />
    <Content Include="ChamCong\NhanVien.aspx" />
    <Content Include="ConsolManifest.aspx" />
    <Content Include="Control\DateTimeWidget.ascx" />
    <Content Include="Control\KHVC.ascx" />
    <Content Include="Control\PopupTTHX.ascx" />
    <Content Include="css\bootstrap-datepicker.css" />
    <Content Include="css\bootstrap-responsive.css" />
    <Content Include="css\bootstrap-theme.css" />
    <Content Include="css\bootstrap-theme.min.css" />
    <Content Include="css\bootstrap.css" />
    <Content Include="css\bootstrap.min.css" />
    <Content Include="css\quanlylichlamviec.css" />
    <Content Include="css\site-master.css" />
    <Content Include="js\canvasjs.min.js" />
    <Content Include="css\dataTables.fixedColumns.css" />
    <Content Include="css\font-awesome.min.css" />
    <Content Include="css\jquery-ui.css" />
    <Content Include="css\jquery.contextMenu.css" />
    <Content Include="css\jquery.dataTables.min.css" />
    <Content Include="css\jquery.timepicker.css" />
    <Content Include="css\magnific-popup.css" />
    <Content Include="css\select2.min.css" />
    <Content Include="css\site.css" />
    <Content Include="DangNhap.aspx" />
    <Content Include="DanhSachDNN.aspx" />
    <Content Include="DataRoot\css\bootstrap-theme.css" />
    <Content Include="DataRoot\css\bootstrap-theme.min.css" />
    <Content Include="DataRoot\css\bootstrap.css" />
    <Content Include="DataRoot\css\bootstrap.min.css" />
    <Content Include="DataRoot\css\dataTables.bootstrap.css" />
    <Content Include="DataRoot\css\dataTables.responsive.css" />
    <Content Include="DataRoot\css\font-awesome.css" />
    <Content Include="DataRoot\css\font-awesome.min.css" />
    <Content Include="DataRoot\css\jquery-ui.css" />
    <Content Include="DataRoot\css\metisMenu.css" />
    <Content Include="DataRoot\css\metisMenu.min.css" />
    <Content Include="DataRoot\css\morris.css" />
    <Content Include="DataRoot\css\sb-admin-2.css" />
    <Content Include="DataRoot\css\timeline.css" />
    <Content Include="DataRoot\fonts\fontawesome-webfont.svg" />
    <Content Include="DataRoot\fonts\glyphicons-halflings-regular.svg">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="DataRoot\js\bootstrap.js" />
    <Content Include="DataRoot\js\bootstrap.min.js" />
    <Content Include="DataRoot\js\dataTables.bootstrap.min.js" />
    <Content Include="DataRoot\js\jquery-ui.js" />
    <Content Include="DataRoot\js\jquery.dataTables.min.js" />
    <Content Include="DataRoot\js\jquery.js" />
    <Content Include="DataRoot\js\jquery.min.js" />
    <Content Include="DataRoot\js\k.js" />
    <Content Include="DataRoot\js\metisMenu.js" />
    <Content Include="DataRoot\js\metisMenu.min.js" />
    <Content Include="DataRoot\js\morris-data.js" />
    <Content Include="DataRoot\js\morris.js" />
    <Content Include="DataRoot\js\morris.min.js" />
    <Content Include="DataRoot\js\npm.js" />
    <Content Include="DataRoot\js\raphael.js" />
    <Content Include="DataRoot\js\sb-admin-2.js" />
    <Content Include="Default.aspx" />
    <Content Include="DNNCHECK.aspx" />
    <Content Include="DoiThongTin.aspx" />
    <Content Include="DoThiNhapHang.aspx" />
    <Content Include="DoThiXuatHang.aspx" />
    <Content Include="DVAIPS.aspx" />
    <Content Include="Dataset\fonts\fontawesome-webfont.svg" />
    <Content Include="images\dsloihx.jpg" />
    <Content Include="images\edit.png" />
    <Content Include="images\header2.jpg" />
    <Content Include="images\khoAa.jpg" />
    <Content Include="images\khoAa1.jpg" />
    <Content Include="images\khoB.jpg" />
    <Content Include="images\khoB1.jpg" />
    <Content Include="images\print.png" />
    <Content Include="images\remove.png" />
    <Content Include="images\sort_asc.png" />
    <Content Include="images\sort_both.png" />
    <Content Include="images\tailieu\file-doc.png" />
    <Content Include="images\tailieu\thumbnail-doc.png" />
    <Content Include="images\tailieu\file-docx.png" />
    <Content Include="images\tailieu\file-download.png" />
    <Content Include="images\tailieu\file-jpg.png" />
    <Content Include="images\tailieu\file-pdf.png" />
    <Content Include="images\tailieu\file-png.png" />
    <Content Include="images\tailieu\file-txt.png" />
    <Content Include="images\tailieu\file-xls.png" />
    <Content Include="images\tailieu\file-xlsx.png" />
    <Content Include="images\thongbaohx.jpg" />
    <Content Include="images\ui-icons_222222_256x240.png" />
    <Content Include="js\bootstrap-datepicker.js" />
    <Content Include="js\bootstrap-treeview.js" />
    <Content Include="js\jquery-ui.js" />
    <Content Include="js\jquery.contextMenu.js" />
    <Content Include="js\jquery.dataTables.min.js" />
    <Content Include="js\jquery.easytabs.min.js" />
    <Content Include="js\jquery.floatThead.min.js" />
    <Content Include="js\jquery.hashchange.min.js" />
    <Content Include="js\jquery.magnific-popup.js" />
    <Content Include="js\jquery.number.min.js" />
    <Content Include="js\jquery.stickytableheaders.min.js" />
    <Content Include="js\jquery.tablesorter.js" />
    <Content Include="js\jquery.timepicker.js" />
    <Content Include="js\jquery.ui.position.js" />
    <Content Include="js\namnh.js" />
    <Content Include="js\quanlylichlamviec.js" />
    <Content Include="js\site-master.js" />
    <Content Include="js\widget-editable.js" />
    <Content Include="KiemSoatHSC.aspx" />
    <Content Include="nbaget\Default.aspx" />
    <Content Include="NhapGTK.aspx" />
    <Content Include="QuanLyChiHo.aspx" />
    <Content Include="QuanLyChiHoNCC.aspx" />
    <Content Include="QuanLyChuyenXeCPN.aspx" />
    <Content Include="QuanLyCongVanHQ.aspx" />
    <Content Include="QuanLyGiaoNhanDOC.aspx" />
    <Content Include="QuanLyHangNhap.aspx" />
    <Content Include="QuanLyHangNhapKhachHang.aspx" />
    <Content Include="QuanLyHangXuat.aspx" />
    <Content Include="QuanLyHangXuatKhachHang.aspx" />
    <Content Include="QuanLyInTem.aspx" />
    <Content Include="QuanLyKhoFM.aspx" />
    <Content Include="QuanLyKhoHang.aspx" />
    <Content Include="QuanLyKHVC2.aspx" />
    <Content Include="QuanLyKHVC3.aspx" />
    <Content Include="QuanLyLogistics.aspx" />
    <Content Include="QuanLyLichLamViec.aspx" />
    <Content Include="QuanLyLoi.aspx" />
    <Content Include="QuanLyPOD.aspx" />
    <Content Include="QuanLyPODView.aspx" />
    <Content Include="QuanLySuatAn.aspx" />
    <Content Include="QuanLySuatAnApp\SuatAnApp.aspx" />
    <Content Include="QuanLyTruck.aspx" />
    <Content Include="QuanLyViTriKhoMoi.aspx" />
    <Content Include="QueryListFFM.aspx" />
    <Content Include="ReportHaiQuan.aspx" />
    <Content Include="ReportQuanLyKhoThuong.aspx" />
    <Content Include="testPrint.aspx" />
    <Content Include="ThanhToan\BaoCao.aspx" />
    <Content Include="ThanhToan\Default.aspx" />
    <Content Include="ThanhToan\KhachHangDoiTac.aspx" />
    <Content Include="ThanhToan\ThanhToan.aspx" />
    <Content Include="TraCuuDienEDI.aspx" />
    <Content Include="TraCuuDNN.aspx" />
    <Content Include="TraCuuLuong\Default.aspx" />
    <Content Include="TraCuuLuong\dist\css\sb-admin-2.css" />
    <Content Include="TraCuuLuong\dist\css\sb-admin-2.min.css" />
    <Content Include="TraCuuLuong\dist\js\sb-admin-2.js" />
    <Content Include="TraCuuLuong\dist\js\sb-admin-2.min.js" />
    <Content Include="TraCuuLuong\js\default.js" />
    <Content Include="TraCuuLuong\js\sb-admin-2.js" />
    <Content Include="TraCuuLuong\vendor\bootstrap-social\bootstrap-social.css" />
    <Content Include="TraCuuLuong\vendor\bootstrap\css\bootstrap.css" />
    <Content Include="TraCuuLuong\vendor\bootstrap\css\bootstrap.min.css" />
    <Content Include="TraCuuLuong\vendor\bootstrap\fonts\glyphicons-halflings-regular.svg" />
    <Content Include="TraCuuLuong\vendor\bootstrap\js\bootstrap.js" />
    <Content Include="TraCuuLuong\vendor\bootstrap\js\bootstrap.min.js" />
    <Content Include="TraCuuLuong\vendor\datatables-plugins\dataTables.bootstrap.css" />
    <Content Include="TraCuuLuong\vendor\datatables-plugins\dataTables.bootstrap.js" />
    <Content Include="TraCuuLuong\vendor\datatables-plugins\dataTables.bootstrap.min.js" />
    <Content Include="TraCuuLuong\vendor\datatables-plugins\index.html" />
    <Content Include="TraCuuLuong\vendor\datatables-responsive\dataTables.responsive.css" />
    <Content Include="TraCuuLuong\vendor\datatables-responsive\dataTables.responsive.js" />
    <Content Include="TraCuuLuong\vendor\datatables\css\dataTables.bootstrap.css" />
    <Content Include="TraCuuLuong\vendor\datatables\css\dataTables.bootstrap.min.css" />
    <Content Include="TraCuuLuong\vendor\datatables\css\dataTables.bootstrap4.css" />
    <Content Include="TraCuuLuong\vendor\datatables\css\dataTables.bootstrap4.min.css" />
    <Content Include="TraCuuLuong\vendor\datatables\css\dataTables.foundation.css" />
    <Content Include="TraCuuLuong\vendor\datatables\css\dataTables.foundation.min.css" />
    <Content Include="TraCuuLuong\vendor\datatables\css\dataTables.jqueryui.css" />
    <Content Include="TraCuuLuong\vendor\datatables\css\dataTables.jqueryui.min.css" />
    <Content Include="TraCuuLuong\vendor\datatables\css\dataTables.material.css" />
    <Content Include="TraCuuLuong\vendor\datatables\css\dataTables.material.min.css" />
    <Content Include="TraCuuLuong\vendor\datatables\css\dataTables.semanticui.css" />
    <Content Include="TraCuuLuong\vendor\datatables\css\dataTables.semanticui.min.css" />
    <Content Include="TraCuuLuong\vendor\datatables\css\dataTables.uikit.css" />
    <Content Include="TraCuuLuong\vendor\datatables\css\dataTables.uikit.min.css" />
    <Content Include="TraCuuLuong\vendor\datatables\css\jquery.dataTables.css" />
    <Content Include="TraCuuLuong\vendor\datatables\css\jquery.dataTables.min.css" />
    <Content Include="TraCuuLuong\vendor\datatables\css\jquery.dataTables_themeroller.css" />
    <Content Include="TraCuuLuong\vendor\datatables\images\favicon.ico" />
    <Content Include="TraCuuLuong\vendor\datatables\images\sort_asc.png" />
    <Content Include="TraCuuLuong\vendor\datatables\images\sort_asc_disabled.png" />
    <Content Include="TraCuuLuong\vendor\datatables\images\sort_both.png" />
    <Content Include="TraCuuLuong\vendor\datatables\images\sort_desc.png" />
    <Content Include="TraCuuLuong\vendor\datatables\images\sort_desc_disabled.png" />
    <Content Include="TraCuuLuong\vendor\datatables\js\dataTables.bootstrap.js" />
    <Content Include="TraCuuLuong\vendor\datatables\js\dataTables.bootstrap.min.js" />
    <Content Include="TraCuuLuong\vendor\datatables\js\dataTables.bootstrap4.js" />
    <Content Include="TraCuuLuong\vendor\datatables\js\dataTables.bootstrap4.min.js" />
    <Content Include="TraCuuLuong\vendor\datatables\js\dataTables.foundation.js" />
    <Content Include="TraCuuLuong\vendor\datatables\js\dataTables.foundation.min.js" />
    <Content Include="TraCuuLuong\vendor\datatables\js\dataTables.jqueryui.js" />
    <Content Include="TraCuuLuong\vendor\datatables\js\dataTables.jqueryui.min.js" />
    <Content Include="TraCuuLuong\vendor\datatables\js\dataTables.material.js" />
    <Content Include="TraCuuLuong\vendor\datatables\js\dataTables.material.min.js" />
    <Content Include="TraCuuLuong\vendor\datatables\js\dataTables.semanticui.js" />
    <Content Include="TraCuuLuong\vendor\datatables\js\dataTables.semanticui.min.js" />
    <Content Include="TraCuuLuong\vendor\datatables\js\dataTables.uikit.js" />
    <Content Include="TraCuuLuong\vendor\datatables\js\dataTables.uikit.min.js" />
    <Content Include="TraCuuLuong\vendor\datatables\js\jquery.dataTables.js" />
    <Content Include="TraCuuLuong\vendor\datatables\js\jquery.dataTables.min.js" />
    <Content Include="TraCuuLuong\vendor\datatables\js\jquery.js" />
    <Content Include="TraCuuLuong\vendor\flot-tooltip\jquery.flot.tooltip.js" />
    <Content Include="TraCuuLuong\vendor\flot-tooltip\jquery.flot.tooltip.min.js" />
    <Content Include="TraCuuLuong\vendor\flot-tooltip\jquery.flot.tooltip.source.js" />
    <Content Include="TraCuuLuong\vendor\flot\excanvas.js" />
    <Content Include="TraCuuLuong\vendor\flot\excanvas.min.js" />
    <Content Include="TraCuuLuong\vendor\flot\jquery.colorhelpers.js" />
    <Content Include="TraCuuLuong\vendor\flot\jquery.flot.canvas.js" />
    <Content Include="TraCuuLuong\vendor\flot\jquery.flot.categories.js" />
    <Content Include="TraCuuLuong\vendor\flot\jquery.flot.crosshair.js" />
    <Content Include="TraCuuLuong\vendor\flot\jquery.flot.errorbars.js" />
    <Content Include="TraCuuLuong\vendor\flot\jquery.flot.fillbetween.js" />
    <Content Include="TraCuuLuong\vendor\flot\jquery.flot.image.js" />
    <Content Include="TraCuuLuong\vendor\flot\jquery.flot.js" />
    <Content Include="TraCuuLuong\vendor\flot\jquery.flot.navigate.js" />
    <Content Include="TraCuuLuong\vendor\flot\jquery.flot.pie.js" />
    <Content Include="TraCuuLuong\vendor\flot\jquery.flot.resize.js" />
    <Content Include="TraCuuLuong\vendor\flot\jquery.flot.selection.js" />
    <Content Include="TraCuuLuong\vendor\flot\jquery.flot.stack.js" />
    <Content Include="TraCuuLuong\vendor\flot\jquery.flot.symbol.js" />
    <Content Include="TraCuuLuong\vendor\flot\jquery.flot.threshold.js" />
    <Content Include="TraCuuLuong\vendor\flot\jquery.flot.time.js" />
    <Content Include="TraCuuLuong\vendor\flot\jquery.js" />
    <Content Include="TraCuuLuong\vendor\font-awesome\css\font-awesome.css" />
    <Content Include="TraCuuLuong\vendor\font-awesome\css\font-awesome.min.css" />
    <Content Include="TraCuuLuong\vendor\font-awesome\fonts\fontawesome-webfont.svg" />
    <Content Include="TraCuuLuong\vendor\font-awesome\HELP-US-OUT.txt" />
    <Content Include="TraCuuLuong\vendor\jquery\jquery.js" />
    <Content Include="TraCuuLuong\vendor\jquery\jquery.min.js" />
    <Content Include="TraCuuLuong\vendor\metisMenu\metisMenu.css" />
    <Content Include="TraCuuLuong\vendor\metisMenu\metisMenu.js" />
    <Content Include="TraCuuLuong\vendor\metisMenu\metisMenu.min.css" />
    <Content Include="TraCuuLuong\vendor\metisMenu\metisMenu.min.js" />
    <Content Include="TraCuuLuong\vendor\morrisjs\morris.css" />
    <Content Include="TraCuuLuong\vendor\morrisjs\morris.js" />
    <Content Include="TraCuuLuong\vendor\morrisjs\morris.min.js" />
    <Content Include="TraCuuLuong\vendor\raphael\raphael.js" />
    <Content Include="TraCuuLuong\vendor\raphael\raphael.min.js" />
    <Content Include="TrangThaiHangNhapASG.aspx" />
    <Content Include="TrangThaiHangXuatASG.aspx" />
    <Content Include="TrienKhaiVanBan.aspx" />
    <Content Include="Truck\GiaBan.aspx" />
    <Content Include="Truck\GiaMua.aspx" />
    <Content Include="Truck\KhachHang.aspx" />
    <Content Include="Truck\TraCuu.aspx" />
    <Content Include="Truck\Tuyen.aspx" />
    <Content Include="TruyVanCPN.aspx" />
    <Content Include="TruyVanDNN.aspx" />
    <Content Include="QueryListInVoices.aspx" />
    <Content Include="TruyVanPO.aspx" />
    <Content Include="UC\ThongBao.ascx" />
    <Content Include="UC\TrangThaiNhanVien.ascx" />
    <Content Include="UpdateFWD.aspx" />
    <Content Include="ViTri.aspx" />
    <Content Include="Dataset\DataSetConsolManifest.xsc">
      <DependentUpon>DataSetConsolManifest.xsd</DependentUpon>
    </Content>
    <Content Include="NhapTextNBA.aspx" />
    <Content Include="XemAnhHoaDonXuat.aspx" />
    <Content Include="XemCanDIM.aspx" />
    <Content Include="XuatBaoCao.aspx" />
    <Content Include="app.config" />
    <Content Include="DataRoot\css\bootstrap-theme.css.map" />
    <Content Include="DataRoot\css\bootstrap.css.map" />
    <Content Include="DataRoot\js\jquery.min.map" />
    <Content Include="DataRoot\fonts\fontawesome-webfont.eot" />
    <Content Include="DataRoot\fonts\fontawesome-webfont.ttf" />
    <Content Include="DataRoot\fonts\fontawesome-webfont.woff" />
    <Content Include="DataRoot\fonts\FontAwesome.otf" />
    <Content Include="DataRoot\fonts\glyphicons-halflings-regular.eot">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="DataRoot\fonts\glyphicons-halflings-regular.ttf">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="DataRoot\fonts\glyphicons-halflings-regular.woff">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="AjaxFileUploader.ashx" />
    <Content Include="ZaloOA\GetZalo.aspx" />
    <Content Include="AjaxReadFilePDF.ashx" />
    <Content Include="App_Data\key-googlesheet.json" />
    <None Include="bundleconfig.json" />
    <Content Include="DataRoot\fonts\glyphicons-halflings-regular.woff2" />
    <None Include="Dataset\DataSetConsolManifest.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>DataSetConsolManifest.Designer.cs</LastGenOutput>
    </None>
    <Content Include="Dataset\DataSetConsolManifest.xss">
      <DependentUpon>DataSetConsolManifest.xsd</DependentUpon>
    </Content>
    <Content Include="Dataset\fonts\fontawesome-webfont.eot" />
    <Content Include="Dataset\fonts\fontawesome-webfont.ttf" />
    <Content Include="Dataset\fonts\fontawesome-webfont.woff" />
    <Content Include="Dataset\fonts\fontawesome-webfont.woff2" />
    <Content Include="Dataset\fonts\FontAwesome.otf" />
    <None Include="Dataset\fonts\glyphicons-halflings-regular.svg">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <Content Include="images\Airport.png" />
    <Content Include="images\alsewh.png" />
    <Content Include="images\ARRIVAL.png" />
    <Content Include="images\blog_accept.png" />
    <Content Include="images\booking.png" />
    <Content Include="images\COMPLETE.png" />
    <Content Include="images\dagiaoxong.png" />
    <Content Include="images\danggiaohang.png" />
    <Content Include="images\DELIVERY PLAN.png" />
    <Content Include="images\DELYVERING.png" />
    <Content Include="images\header.jpg" />
    <Content Include="images\hoanthanhhaiquan.png" />
    <Content Include="images\mail_web.png" />
    <Content Include="images\pre-accept.png" />
    <Content Include="images\SHIPPING.png" />
    <Content Include="images\truck.png" />
    <Content Include="images\TRUCKING ALSE.png" />
    <Content Include="images\TRUCKINGTOALSE.png" />
    <Content Include="css\jquery.handsontable.full.css" />
    <Content Include="css\samples.css" />
    <Content Include="js\jquery.droptabs.js" />
    <Content Include="js\jquery.droptabs.min.js" />
    <Content Include="js\jquery.formance.min.js" />
    <Content Include="js\jquery.handsontable.full.js" />
    <Content Include="js\jquery.maskedinput.js" />
    <Content Include="NhapDaiLy.aspx" />
    <Content Include="QuanLyKHVC.aspx" />
    <Content Include="TrangThaiHangNhap.aspx" />
    <Content Include="images\597997-1680x1050.jpg" />
    <Content Include="images\bg.jpg" />
    <Content Include="images\document_accept.png" />
    <Content Include="images\falcon.png" />
    <Content Include="images\lorrygreen.png" />
    <Content Include="images\plane-icon.png" />
    <Content Include="js\bootstrap.js" />
    <Content Include="js\bootstrap.min.js" />
    <Content Include="KeHoachLayHang.aspx" />
    <Content Include="QuanLyPhieuCan.aspx" />
    <Content Include="Global.asax" />
    <Content Include="Thoat.aspx" />
    <Content Include="TrangThaiHangXuat.aspx" />
    <Content Include="TruyVanHangXuat.aspx" />
    <Content Include="Web.config">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="Web.Debug.config">
      <DependentUpon>Web.config</DependentUpon>
    </Content>
    <Content Include="Web.Release.config">
      <DependentUpon>Web.config</DependentUpon>
    </Content>
    <Content Include="TraCuuLuong\vendor\bootstrap-social\bootstrap-social.less" />
    <Content Include="TraCuuLuong\vendor\bootstrap-social\bootstrap-social.scss" />
    <Content Include="TraCuuLuong\vendor\bootstrap\fonts\glyphicons-halflings-regular.eot" />
    <Content Include="TraCuuLuong\vendor\bootstrap\fonts\glyphicons-halflings-regular.ttf" />
    <Content Include="TraCuuLuong\vendor\bootstrap\fonts\glyphicons-halflings-regular.woff" />
    <Content Include="TraCuuLuong\vendor\bootstrap\fonts\glyphicons-halflings-regular.woff2" />
    <Content Include="TraCuuLuong\vendor\datatables-responsive\dataTables.responsive.scss" />
    <Content Include="TraCuuLuong\vendor\datatables\images\Sorting icons.psd" />
    <Content Include="TraCuuLuong\vendor\font-awesome\css\font-awesome.css.map" />
    <Content Include="TraCuuLuong\vendor\font-awesome\fonts\fontawesome-webfont.eot" />
    <Content Include="TraCuuLuong\vendor\font-awesome\fonts\fontawesome-webfont.ttf" />
    <Content Include="TraCuuLuong\vendor\font-awesome\fonts\fontawesome-webfont.woff" />
    <Content Include="TraCuuLuong\vendor\font-awesome\fonts\fontawesome-webfont.woff2" />
    <Content Include="TraCuuLuong\vendor\font-awesome\fonts\FontAwesome.otf" />
    <Content Include="TraCuuLuong\vendor\font-awesome\less\animated.less" />
    <Content Include="TraCuuLuong\vendor\font-awesome\less\bordered-pulled.less" />
    <Content Include="TraCuuLuong\vendor\font-awesome\less\core.less" />
    <Content Include="TraCuuLuong\vendor\font-awesome\less\extras.less" />
    <Content Include="TraCuuLuong\vendor\font-awesome\less\fixed-width.less" />
    <Content Include="TraCuuLuong\vendor\font-awesome\less\font-awesome.less" />
    <Content Include="TraCuuLuong\vendor\font-awesome\less\icons.less" />
    <Content Include="TraCuuLuong\vendor\font-awesome\less\larger.less" />
    <Content Include="TraCuuLuong\vendor\font-awesome\less\list.less" />
    <Content Include="TraCuuLuong\vendor\font-awesome\less\mixins.less" />
    <Content Include="TraCuuLuong\vendor\font-awesome\less\path.less" />
    <Content Include="TraCuuLuong\vendor\font-awesome\less\rotated-flipped.less" />
    <Content Include="TraCuuLuong\vendor\font-awesome\less\screen-reader.less" />
    <Content Include="TraCuuLuong\vendor\font-awesome\less\spinning.less" />
    <Content Include="TraCuuLuong\vendor\font-awesome\less\stacked.less" />
    <Content Include="TraCuuLuong\vendor\font-awesome\less\variables.less" />
    <Content Include="TraCuuLuong\vendor\font-awesome\scss\font-awesome.scss" />
    <Content Include="TraCuuLuong\vendor\font-awesome\scss\_animated.scss" />
    <Content Include="TraCuuLuong\vendor\font-awesome\scss\_bordered-pulled.scss" />
    <Content Include="TraCuuLuong\vendor\font-awesome\scss\_core.scss" />
    <Content Include="TraCuuLuong\vendor\font-awesome\scss\_extras.scss" />
    <Content Include="TraCuuLuong\vendor\font-awesome\scss\_fixed-width.scss" />
    <Content Include="TraCuuLuong\vendor\font-awesome\scss\_icons.scss" />
    <Content Include="TraCuuLuong\vendor\font-awesome\scss\_larger.scss" />
    <Content Include="TraCuuLuong\vendor\font-awesome\scss\_list.scss" />
    <Content Include="TraCuuLuong\vendor\font-awesome\scss\_mixins.scss" />
    <Content Include="TraCuuLuong\vendor\font-awesome\scss\_path.scss" />
    <Content Include="TraCuuLuong\vendor\font-awesome\scss\_rotated-flipped.scss" />
    <Content Include="TraCuuLuong\vendor\font-awesome\scss\_screen-reader.scss" />
    <Content Include="TraCuuLuong\vendor\font-awesome\scss\_spinning.scss" />
    <Content Include="TraCuuLuong\vendor\font-awesome\scss\_stacked.scss" />
    <Content Include="TraCuuLuong\vendor\font-awesome\scss\_variables.scss" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Admin\QuanLyNhanVien.aspx.cs">
      <DependentUpon>QuanLyNhanVien.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Admin\QuanLyNhanVien.aspx.designer.cs">
      <DependentUpon>QuanLyNhanVien.aspx</DependentUpon>
    </Compile>
    <Compile Include="Admin\QuanLyNhomTaiKhoan.aspx.cs">
      <DependentUpon>QuanLyNhomTaiKhoan.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Admin\QuanLyNhomTaiKhoan.aspx.designer.cs">
      <DependentUpon>QuanLyNhomTaiKhoan.aspx</DependentUpon>
    </Compile>
    <Compile Include="Admin\QuanLyPhanQuyen.aspx.cs">
      <DependentUpon>QuanLyPhanQuyen.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Admin\QuanLyPhanQuyen.aspx.designer.cs">
      <DependentUpon>QuanLyPhanQuyen.aspx</DependentUpon>
    </Compile>
    <Compile Include="AjaxFileUploader.ashx.cs">
      <DependentUpon>AjaxFileUploader.ashx</DependentUpon>
    </Compile>
    <Compile Include="AjaxReadFilePDF.ashx.cs">
      <DependentUpon>AjaxReadFilePDF.ashx</DependentUpon>
    </Compile>
    <Compile Include="AWBView.aspx.cs">
      <DependentUpon>AWBView.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="AWBView.aspx.designer.cs">
      <DependentUpon>AWBView.aspx</DependentUpon>
    </Compile>
    <Compile Include="BaoCaoOther.aspx.cs">
      <DependentUpon>BaoCaoOther.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BaoCaoOther.aspx.designer.cs">
      <DependentUpon>BaoCaoOther.aspx</DependentUpon>
    </Compile>
    <Compile Include="BilldingvsUpdate.aspx.cs">
      <DependentUpon>BilldingvsUpdate.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BilldingvsUpdate.aspx.designer.cs">
      <DependentUpon>BilldingvsUpdate.aspx</DependentUpon>
    </Compile>
    <Compile Include="ChamCong\BangChamCongLoi.aspx.cs">
      <DependentUpon>BangChamCongLoi.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="ChamCong\BangChamCongLoi.aspx.designer.cs">
      <DependentUpon>BangChamCongLoi.aspx</DependentUpon>
    </Compile>
    <Compile Include="ChamCong\BangTin.aspx.cs">
      <DependentUpon>BangTin.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="ChamCong\BangTin.aspx.designer.cs">
      <DependentUpon>BangTin.aspx</DependentUpon>
    </Compile>
    <Compile Include="ChamCong\DiMuon.aspx.cs">
      <DependentUpon>DiMuon.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="ChamCong\DiMuon.aspx.designer.cs">
      <DependentUpon>DiMuon.aspx</DependentUpon>
    </Compile>
    <Compile Include="ChamCong\XuatBangChamCong.aspx.cs">
      <DependentUpon>XuatBangChamCong.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="ChamCong\XuatBangChamCong.aspx.designer.cs">
      <DependentUpon>XuatBangChamCong.aspx</DependentUpon>
    </Compile>
    <Compile Include="ChuyenPhatNhanhCPN.aspx.cs">
      <DependentUpon>ChuyenPhatNhanhCPN.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="ChuyenPhatNhanhCPN.aspx.designer.cs">
      <DependentUpon>ChuyenPhatNhanhCPN.aspx</DependentUpon>
    </Compile>
    <Compile Include="ChuyenPhatNhanh_V2.aspx.cs">
      <DependentUpon>ChuyenPhatNhanh_V2.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="ChuyenPhatNhanh_V2.aspx.designer.cs">
      <DependentUpon>ChuyenPhatNhanh_V2.aspx</DependentUpon>
    </Compile>
    <Compile Include="CongViec\BangTin.aspx.cs">
      <DependentUpon>BangTin.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="CongViec\BangTin.aspx.designer.cs">
      <DependentUpon>BangTin.aspx</DependentUpon>
    </Compile>
    <Compile Include="CPNView.aspx.cs">
      <DependentUpon>CPNView.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="CPNView.aspx.designer.cs">
      <DependentUpon>CPNView.aspx</DependentUpon>
    </Compile>
    <Compile Include="cs\SIUD2.cs" />
    <Compile Include="DangKyVaoRa.aspx.cs">
      <DependentUpon>DangKyVaoRa.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="DangKyVaoRa.aspx.designer.cs">
      <DependentUpon>DangKyVaoRa.aspx</DependentUpon>
    </Compile>
    <Compile Include="DanhSachVatTu\DanhSachVatTu.aspx.cs">
      <DependentUpon>DanhSachVatTu.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="DanhSachVatTu\DanhSachVatTu.aspx.designer.cs">
      <DependentUpon>DanhSachVatTu.aspx</DependentUpon>
    </Compile>
    <Compile Include="DanhSachVatTu\DeNghiCapPhat.aspx.cs">
      <DependentUpon>DeNghiCapPhat.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="DanhSachVatTu\DeNghiCapPhat.aspx.designer.cs">
      <DependentUpon>DeNghiCapPhat.aspx</DependentUpon>
    </Compile>
    <Compile Include="DanhSachVatTu\QuanLyNhapVatTu.aspx.cs">
      <DependentUpon>QuanLyNhapVatTu.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="DanhSachVatTu\QuanLyNhapVatTu.aspx.designer.cs">
      <DependentUpon>QuanLyNhapVatTu.aspx</DependentUpon>
    </Compile>
    <Compile Include="Distribution\DeliveryOrders.aspx.cs">
      <DependentUpon>DeliveryOrders.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Distribution\DeliveryOrders.aspx.designer.cs">
      <DependentUpon>DeliveryOrders.aspx</DependentUpon>
    </Compile>
    <Compile Include="Distribution\Route.aspx.cs">
      <DependentUpon>Route.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Distribution\Route.aspx.designer.cs">
      <DependentUpon>Route.aspx</DependentUpon>
    </Compile>
    <Compile Include="DownloadCacheFile.aspx.cs">
      <DependentUpon>DownloadCacheFile.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="DownloadCacheFile.aspx.designer.cs">
      <DependentUpon>DownloadCacheFile.aspx</DependentUpon>
    </Compile>
    <Compile Include="ERP.aspx.cs">
      <DependentUpon>ERP.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="ERP.aspx.designer.cs">
      <DependentUpon>ERP.aspx</DependentUpon>
    </Compile>
    <Compile Include="Distribution\Customers.aspx.cs">
      <DependentUpon>Customers.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Distribution\Customers.aspx.designer.cs">
      <DependentUpon>Customers.aspx</DependentUpon>
    </Compile>
    <Compile Include="Distribution\Products.aspx.cs">
      <DependentUpon>Products.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Distribution\Products.aspx.designer.cs">
      <DependentUpon>Products.aspx</DependentUpon>
    </Compile>
    <Compile Include="inlabel.aspx.cs">
      <DependentUpon>inlabel.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="inlabel.aspx.designer.cs">
      <DependentUpon>inlabel.aspx</DependentUpon>
    </Compile>
    <Compile Include="InputExcel2.aspx.cs">
      <DependentUpon>InputExcel2.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="InputExcel2.aspx.designer.cs">
      <DependentUpon>InputExcel2.aspx</DependentUpon>
    </Compile>
    <Compile Include="InputExcelBook.aspx.cs">
      <DependentUpon>InputExcelBook.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="InputExcelBook.aspx.designer.cs">
      <DependentUpon>InputExcelBook.aspx</DependentUpon>
    </Compile>
    <Compile Include="InputExcelBookVsip.aspx.cs">
      <DependentUpon>InputExcelBookVsip.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="InputExcelBookVsip.aspx.designer.cs">
      <DependentUpon>InputExcelBookVsip.aspx</DependentUpon>
    </Compile>
    <Compile Include="IP.aspx.cs">
      <DependentUpon>IP.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="IP.aspx.designer.cs">
      <DependentUpon>IP.aspx</DependentUpon>
    </Compile>
    <Compile Include="KeHoachDetails.aspx.cs">
      <DependentUpon>KeHoachDetails.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="KeHoachDetails.aspx.designer.cs">
      <DependentUpon>KeHoachDetails.aspx</DependentUpon>
    </Compile>
    <Compile Include="KhaiBaoYTe\cs\ftp.cs" />
    <Compile Include="KhaiBaoYTe\cs\HamDungChung.cs" />
    <Compile Include="KhaiBaoYTe\cs\Pr.cs" />
    <Compile Include="KhaiBaoYTe\cs\SIUD.cs" />
    <Compile Include="KhaiBaoYTe\Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="KhaiBaoYTe\Default.aspx.designer.cs">
      <DependentUpon>Default.aspx</DependentUpon>
    </Compile>
    <Compile Include="KhaiBaoYTe\KhaiBao.aspx.cs">
      <DependentUpon>KhaiBao.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="KhaiBaoYTe\KhaiBao.aspx.designer.cs">
      <DependentUpon>KhaiBao.aspx</DependentUpon>
    </Compile>
    <Compile Include="KhaiBaoYTe\KhaiBaoYTe.Master.cs">
      <DependentUpon>KhaiBaoYTe.Master</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="KhaiBaoYTe\KhaiBaoYTe.Master.designer.cs">
      <DependentUpon>KhaiBaoYTe.Master</DependentUpon>
    </Compile>
    <Compile Include="KhaiBaoYTe\QuanLyKhaiBao.aspx.cs">
      <DependentUpon>QuanLyKhaiBao.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="KhaiBaoYTe\QuanLyKhaiBao.aspx.designer.cs">
      <DependentUpon>QuanLyKhaiBao.aspx</DependentUpon>
    </Compile>
    <Compile Include="KhoVsip1.aspx.cs">
      <DependentUpon>KhoVsip1.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="KhoVsip1.aspx.designer.cs">
      <DependentUpon>KhoVsip1.aspx</DependentUpon>
    </Compile>
    <Compile Include="KhoVsip2.aspx.cs">
      <DependentUpon>KhoVsip2.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="KhoVsip2.aspx.designer.cs">
      <DependentUpon>KhoVsip2.aspx</DependentUpon>
    </Compile>
    <Compile Include="OPS.aspx.cs">
      <DependentUpon>OPS.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="OPS.aspx.designer.cs">
      <DependentUpon>OPS.aspx</DependentUpon>
    </Compile>
    <Compile Include="PdfViewer.aspx.cs">
      <DependentUpon>PdfViewer.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="PdfViewer.aspx.designer.cs">
      <DependentUpon>PdfViewer.aspx</DependentUpon>
    </Compile>
    <Compile Include="PODView.aspx.cs">
      <DependentUpon>PODView.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="PODView.aspx.designer.cs">
      <DependentUpon>PODView.aspx</DependentUpon>
    </Compile>
    <Compile Include="QRCode.aspx.cs">
      <DependentUpon>QRCode.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="QRCode.aspx.designer.cs">
      <DependentUpon>QRCode.aspx</DependentUpon>
    </Compile>
    <Compile Include="QuanLyAPPLEKeHoach.aspx.cs">
      <DependentUpon>QuanLyAPPLEKeHoach.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="QuanLyAPPLEKeHoach.aspx.designer.cs">
      <DependentUpon>QuanLyAPPLEKeHoach.aspx</DependentUpon>
    </Compile>
    <Compile Include="QuanLyAPPLEXuatKho.aspx.cs">
      <DependentUpon>QuanLyAPPLEXuatKho.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="QuanLyAPPLEXuatKho.aspx.designer.cs">
      <DependentUpon>QuanLyAPPLEXuatKho.aspx</DependentUpon>
    </Compile>
    <Compile Include="QuanLyBaoCaoNgay.aspx.cs">
      <DependentUpon>QuanLyBaoCaoNgay.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="QuanLyBaoCaoNgay.aspx.designer.cs">
      <DependentUpon>QuanLyBaoCaoNgay.aspx</DependentUpon>
    </Compile>
    <Compile Include="cs\FireMessenger.cs" />
    <Compile Include="cs\ftp.cs" />
    <Compile Include="cs\GS.cs" />
    <Compile Include="cs\VersionedFiles.cs" />
    <Compile Include="cs\VersionNumber.cs" />
    <Compile Include="DGR.aspx.cs">
      <DependentUpon>DGR.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="DGR.aspx.designer.cs">
      <DependentUpon>DGR.aspx</DependentUpon>
    </Compile>
    <Compile Include="DownloadFile.aspx.cs">
      <DependentUpon>DownloadFile.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="DownloadFile.aspx.designer.cs">
      <DependentUpon>DownloadFile.aspx</DependentUpon>
    </Compile>
    <Compile Include="Export.aspx.cs">
      <DependentUpon>Export.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Export.aspx.designer.cs">
      <DependentUpon>Export.aspx</DependentUpon>
    </Compile>
    <Compile Include="ImportStatus.aspx.cs">
      <DependentUpon>ImportStatus.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="ImportStatus.aspx.designer.cs">
      <DependentUpon>ImportStatus.aspx</DependentUpon>
    </Compile>
    <Compile Include="InputExcel.aspx.cs">
      <DependentUpon>InputExcel.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="InputExcel.aspx.designer.cs">
      <DependentUpon>InputExcel.aspx</DependentUpon>
    </Compile>
    <Compile Include="KiemSoatHangSoiChieu.aspx.cs">
      <DependentUpon>KiemSoatHangSoiChieu.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="KiemSoatHangSoiChieu.aspx.designer.cs">
      <DependentUpon>KiemSoatHangSoiChieu.aspx</DependentUpon>
    </Compile>
    <Compile Include="Admin\QuanLyTaiKhoan.aspx.cs">
      <DependentUpon>QuanLyTaiKhoan.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Admin\QuanLyTaiKhoan.aspx.designer.cs">
      <DependentUpon>QuanLyTaiKhoan.aspx</DependentUpon>
    </Compile>
    <Compile Include="ChamCong\BangChamCong.aspx.cs">
      <DependentUpon>BangChamCong.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="ChamCong\BangChamCong.aspx.designer.cs">
      <DependentUpon>BangChamCong.aspx</DependentUpon>
    </Compile>
    <Compile Include="BangTongHopDVASG.aspx.cs">
      <DependentUpon>BangTongHopDVASG.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BangTongHopDVASG.aspx.designer.cs">
      <DependentUpon>BangTongHopDVASG.aspx</DependentUpon>
    </Compile>
    <Compile Include="BaoCaoXuatKho.aspx.cs">
      <DependentUpon>BaoCaoXuatKho.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BaoCaoXuatKho.aspx.designer.cs">
      <DependentUpon>BaoCaoXuatKho.aspx</DependentUpon>
    </Compile>
    <Compile Include="BaoCaoTongHop.aspx.cs">
      <DependentUpon>BaoCaoTongHop.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="BaoCaoTongHop.aspx.designer.cs">
      <DependentUpon>BaoCaoTongHop.aspx</DependentUpon>
    </Compile>
    <Compile Include="ChamCong\TrangThai.aspx.cs">
      <DependentUpon>TrangThai.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="ChamCong\TrangThai.aspx.designer.cs">
      <DependentUpon>TrangThai.aspx</DependentUpon>
    </Compile>
    <Compile Include="ChamCong\NhanVien.aspx.cs">
      <DependentUpon>NhanVien.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="ChamCong\NhanVien.aspx.designer.cs">
      <DependentUpon>NhanVien.aspx</DependentUpon>
    </Compile>
    <Compile Include="ConsolManifest.aspx.cs">
      <DependentUpon>ConsolManifest.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="ConsolManifest.aspx.designer.cs">
      <DependentUpon>ConsolManifest.aspx</DependentUpon>
    </Compile>
    <Compile Include="Control\DateTimeWidget.ascx.cs">
      <DependentUpon>DateTimeWidget.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Control\DateTimeWidget.ascx.designer.cs">
      <DependentUpon>DateTimeWidget.ascx</DependentUpon>
    </Compile>
    <Compile Include="Control\KHVC.ascx.cs">
      <DependentUpon>KHVC.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Control\KHVC.ascx.designer.cs">
      <DependentUpon>KHVC.ascx</DependentUpon>
    </Compile>
    <Compile Include="Control\PopupTTHX.ascx.cs">
      <DependentUpon>PopupTTHX.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Control\PopupTTHX.ascx.designer.cs">
      <DependentUpon>PopupTTHX.ascx</DependentUpon>
    </Compile>
    <Compile Include="cs\ConnReturnDS.cs" />
    <Compile Include="cs\ConnReturnDSwP.cs" />
    <Compile Include="cs\DnnCheck.cs" />
    <Compile Include="cs\HamDungChung.cs" />
    <Compile Include="cs\Pr.cs" />
    <Compile Include="cs\SIUD.cs" />
    <Compile Include="DangNhap.aspx.cs">
      <DependentUpon>DangNhap.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="DangNhap.aspx.designer.cs">
      <DependentUpon>DangNhap.aspx</DependentUpon>
    </Compile>
    <Compile Include="DanhSachDNN.aspx.cs">
      <DependentUpon>DanhSachDNN.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="DanhSachDNN.aspx.designer.cs">
      <DependentUpon>DanhSachDNN.aspx</DependentUpon>
    </Compile>
    <Compile Include="cs\DataProvider.cs" />
    <Compile Include="DataRoot\cs\StoreGet.cs" />
    <Compile Include="Dataset\DataSetConsolManifest.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>DataSetConsolManifest.xsd</DependentUpon>
    </Compile>
    <Compile Include="Dataset\DataSetHX.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>DataSetHX.xsd</DependentUpon>
    </Compile>
    <Compile Include="Dataset\DataSetSoLieu.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>DataSetSoLieu.xsd</DependentUpon>
    </Compile>
    <Compile Include="Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Default.aspx.designer.cs">
      <DependentUpon>Default.aspx</DependentUpon>
    </Compile>
    <Compile Include="DNNCHECK.aspx.cs">
      <DependentUpon>DNNCHECK.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="DNNCHECK.aspx.designer.cs">
      <DependentUpon>DNNCHECK.aspx</DependentUpon>
    </Compile>
    <Compile Include="DoiThongTin.aspx.cs">
      <DependentUpon>DoiThongTin.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="DoiThongTin.aspx.designer.cs">
      <DependentUpon>DoiThongTin.aspx</DependentUpon>
    </Compile>
    <Compile Include="DoThiNhapHang.aspx.cs">
      <DependentUpon>DoThiNhapHang.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="DoThiNhapHang.aspx.designer.cs">
      <DependentUpon>DoThiNhapHang.aspx</DependentUpon>
    </Compile>
    <Compile Include="DoThiXuatHang.aspx.cs">
      <DependentUpon>DoThiXuatHang.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="DoThiXuatHang.aspx.designer.cs">
      <DependentUpon>DoThiXuatHang.aspx</DependentUpon>
    </Compile>
    <Compile Include="DVAIPS.aspx.cs">
      <DependentUpon>DVAIPS.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="DVAIPS.aspx.designer.cs">
      <DependentUpon>DVAIPS.aspx</DependentUpon>
    </Compile>
    <Compile Include="Global.asax.cs">
      <DependentUpon>Global.asax</DependentUpon>
    </Compile>
    <Compile Include="HangDaVeKho.aspx.cs">
      <DependentUpon>TrangThaiHangNhap.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="HangDaVeKho.aspx.designer.cs">
      <DependentUpon>TrangThaiHangNhap.aspx</DependentUpon>
    </Compile>
    <Compile Include="KeHoachLayHang.aspx.cs">
      <DependentUpon>KeHoachLayHang.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="KeHoachLayHang.aspx.designer.cs">
      <DependentUpon>KeHoachLayHang.aspx</DependentUpon>
    </Compile>
    <Compile Include="KiemSoatHSC.aspx.cs">
      <DependentUpon>KiemSoatHSC.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="KiemSoatHSC.aspx.designer.cs">
      <DependentUpon>KiemSoatHSC.aspx</DependentUpon>
    </Compile>
    <Compile Include="nbaget\Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="nbaget\Default.aspx.designer.cs">
      <DependentUpon>Default.aspx</DependentUpon>
    </Compile>
    <Compile Include="NhapDaiLy.aspx.cs">
      <DependentUpon>NhapDaiLy.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="NhapDaiLy.aspx.designer.cs">
      <DependentUpon>NhapDaiLy.aspx</DependentUpon>
    </Compile>
    <Compile Include="NhapGTK.aspx.cs">
      <DependentUpon>NhapGTK.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="NhapGTK.aspx.designer.cs">
      <DependentUpon>NhapGTK.aspx</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
      <DependentUpon>Settings.settings</DependentUpon>
    </Compile>
    <Compile Include="QuanLyChiHo.aspx.cs">
      <DependentUpon>QuanLyChiHo.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="QuanLyChiHo.aspx.designer.cs">
      <DependentUpon>QuanLyChiHo.aspx</DependentUpon>
    </Compile>
    <Compile Include="QuanLyChiHoNCC.aspx.cs">
      <DependentUpon>QuanLyChiHoNCC.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="QuanLyChiHoNCC.aspx.designer.cs">
      <DependentUpon>QuanLyChiHoNCC.aspx</DependentUpon>
    </Compile>
    <Compile Include="QuanLyChuyenXeCPN.aspx.cs">
      <DependentUpon>QuanLyChuyenXeCPN.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="QuanLyChuyenXeCPN.aspx.designer.cs">
      <DependentUpon>QuanLyChuyenXeCPN.aspx</DependentUpon>
    </Compile>
    <Compile Include="QuanLyCongVanHQ.aspx.cs">
      <DependentUpon>QuanLyCongVanHQ.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="QuanLyCongVanHQ.aspx.designer.cs">
      <DependentUpon>QuanLyCongVanHQ.aspx</DependentUpon>
    </Compile>
    <Compile Include="QuanLyGiaoNhanDOC.aspx.cs">
      <DependentUpon>QuanLyGiaoNhanDOC.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="QuanLyGiaoNhanDOC.aspx.designer.cs">
      <DependentUpon>QuanLyGiaoNhanDOC.aspx</DependentUpon>
    </Compile>
    <Compile Include="QuanLyHangNhap.aspx.cs">
      <DependentUpon>QuanLyHangNhap.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="QuanLyHangNhap.aspx.designer.cs">
      <DependentUpon>QuanLyHangNhap.aspx</DependentUpon>
    </Compile>
    <Compile Include="QuanLyHangNhapKhachHang.aspx.cs">
      <DependentUpon>QuanLyHangNhapKhachHang.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="QuanLyHangNhapKhachHang.aspx.designer.cs">
      <DependentUpon>QuanLyHangNhapKhachHang.aspx</DependentUpon>
    </Compile>
    <Compile Include="QuanLyHangXuat.aspx.cs">
      <DependentUpon>QuanLyHangXuat.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="QuanLyHangXuat.aspx.designer.cs">
      <DependentUpon>QuanLyHangXuat.aspx</DependentUpon>
    </Compile>
    <Compile Include="QuanLyHangXuatKhachHang.aspx.cs">
      <DependentUpon>QuanLyHangXuatKhachHang.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="QuanLyHangXuatKhachHang.aspx.designer.cs">
      <DependentUpon>QuanLyHangXuatKhachHang.aspx</DependentUpon>
    </Compile>
    <Compile Include="QuanLyInTem.aspx.cs">
      <DependentUpon>QuanLyInTem.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="QuanLyInTem.aspx.designer.cs">
      <DependentUpon>QuanLyInTem.aspx</DependentUpon>
    </Compile>
    <Compile Include="QuanLyKhoFM.aspx.cs">
      <DependentUpon>QuanLyKhoFM.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="QuanLyKhoFM.aspx.designer.cs">
      <DependentUpon>QuanLyKhoFM.aspx</DependentUpon>
    </Compile>
    <Compile Include="QuanLyKhoHang.aspx.cs">
      <DependentUpon>QuanLyKhoHang.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="QuanLyKhoHang.aspx.designer.cs">
      <DependentUpon>QuanLyKhoHang.aspx</DependentUpon>
    </Compile>
    <Compile Include="QuanLyKHVC2.aspx.cs">
      <DependentUpon>QuanLyKHVC2.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="QuanLyKHVC2.aspx.designer.cs">
      <DependentUpon>QuanLyKHVC2.aspx</DependentUpon>
    </Compile>
    <Compile Include="QuanLyKHVC3.aspx.cs">
      <DependentUpon>QuanLyKHVC3.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="QuanLyKHVC3.aspx.designer.cs">
      <DependentUpon>QuanLyKHVC3.aspx</DependentUpon>
    </Compile>
    <Compile Include="QuanLyLogistics.aspx.cs">
      <DependentUpon>QuanLyLogistics.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="QuanLyLogistics.aspx.designer.cs">
      <DependentUpon>QuanLyLogistics.aspx</DependentUpon>
    </Compile>
    <Compile Include="QuanLyKHVC.aspx.cs">
      <DependentUpon>QuanLyKHVC.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="QuanLyKHVC.aspx.designer.cs">
      <DependentUpon>QuanLyKHVC.aspx</DependentUpon>
    </Compile>
    <Compile Include="QuanLyLichLamViec.aspx.cs">
      <DependentUpon>QuanLyLichLamViec.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="QuanLyLichLamViec.aspx.designer.cs">
      <DependentUpon>QuanLyLichLamViec.aspx</DependentUpon>
    </Compile>
    <Compile Include="QuanLyLoi.aspx.cs">
      <DependentUpon>QuanLyLoi.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="QuanLyLoi.aspx.designer.cs">
      <DependentUpon>QuanLyLoi.aspx</DependentUpon>
    </Compile>
    <Compile Include="QuanLyPhieuCan.aspx.cs">
      <DependentUpon>QuanLyPhieuCan.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="QuanLyPhieuCan.aspx.designer.cs">
      <DependentUpon>QuanLyPhieuCan.aspx</DependentUpon>
    </Compile>
    <Compile Include="QuanLyPOD.aspx.cs">
      <DependentUpon>QuanLyPOD.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="QuanLyPOD.aspx.designer.cs">
      <DependentUpon>QuanLyPOD.aspx</DependentUpon>
    </Compile>
    <Compile Include="QuanLyPODView.aspx.cs">
      <DependentUpon>QuanLyPODView.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="QuanLyPODView.aspx.designer.cs">
      <DependentUpon>QuanLyPODView.aspx</DependentUpon>
    </Compile>
    <Compile Include="QuanLySuatAn.aspx.cs">
      <DependentUpon>QuanLySuatAn.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="QuanLySuatAn.aspx.designer.cs">
      <DependentUpon>QuanLySuatAn.aspx</DependentUpon>
    </Compile>
    <Compile Include="QuanLySuatAnApp\SuatAnApp.aspx.cs">
      <DependentUpon>SuatAnApp.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="QuanLySuatAnApp\SuatAnApp.aspx.designer.cs">
      <DependentUpon>SuatAnApp.aspx</DependentUpon>
    </Compile>
    <Compile Include="QuanLyTruck.aspx.cs">
      <DependentUpon>QuanLyTruck.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="QuanLyTruck.aspx.designer.cs">
      <DependentUpon>QuanLyTruck.aspx</DependentUpon>
    </Compile>
    <Compile Include="QuanLyViTriKhoMoi.aspx.cs">
      <DependentUpon>QuanLyViTriKhoMoi.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="QuanLyViTriKhoMoi.aspx.designer.cs">
      <DependentUpon>QuanLyViTriKhoMoi.aspx</DependentUpon>
    </Compile>
    <Compile Include="QueryListFFM.aspx.cs">
      <DependentUpon>QueryListFFM.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="QueryListFFM.aspx.designer.cs">
      <DependentUpon>QueryListFFM.aspx</DependentUpon>
    </Compile>
    <Compile Include="ReportHaiQuan.aspx.cs">
      <DependentUpon>ReportHaiQuan.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="ReportHaiQuan.aspx.designer.cs">
      <DependentUpon>ReportHaiQuan.aspx</DependentUpon>
    </Compile>
    <Compile Include="ReportQuanLyKhoThuong.aspx.cs">
      <DependentUpon>ReportQuanLyKhoThuong.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="ReportQuanLyKhoThuong.aspx.designer.cs">
      <DependentUpon>ReportQuanLyKhoThuong.aspx</DependentUpon>
    </Compile>
    <Compile Include="Report\XtraReportCM.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Report\XtraReportCM.Designer.cs">
      <DependentUpon>XtraReportCM.cs</DependentUpon>
    </Compile>
    <Compile Include="Report\XtraReportBCHX.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Report\XtraReportDNN.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Site.Master.cs">
      <DependentUpon>Site.Master</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Site.Master.designer.cs">
      <DependentUpon>Site.Master</DependentUpon>
    </Compile>
    <Compile Include="cs\UserLogin.cs" />
    <Compile Include="testPrint.aspx.cs">
      <DependentUpon>testPrint.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="testPrint.aspx.designer.cs">
      <DependentUpon>testPrint.aspx</DependentUpon>
    </Compile>
    <Compile Include="ThanhToan\BaoCao.aspx.cs">
      <DependentUpon>BaoCao.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="ThanhToan\BaoCao.aspx.designer.cs">
      <DependentUpon>BaoCao.aspx</DependentUpon>
    </Compile>
    <Compile Include="ThanhToan\Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="ThanhToan\Default.aspx.designer.cs">
      <DependentUpon>Default.aspx</DependentUpon>
    </Compile>
    <Compile Include="ThanhToan\KhachHangDoiTac.aspx.cs">
      <DependentUpon>KhachHangDoiTac.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="ThanhToan\KhachHangDoiTac.aspx.designer.cs">
      <DependentUpon>KhachHangDoiTac.aspx</DependentUpon>
    </Compile>
    <Compile Include="ThanhToan\ThanhToan.aspx.cs">
      <DependentUpon>ThanhToan.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="ThanhToan\ThanhToan.aspx.designer.cs">
      <DependentUpon>ThanhToan.aspx</DependentUpon>
    </Compile>
    <Compile Include="Thoat.aspx.cs">
      <DependentUpon>Thoat.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Thoat.aspx.designer.cs">
      <DependentUpon>Thoat.aspx</DependentUpon>
    </Compile>
    <Compile Include="TraCuuDienEDI.aspx.cs">
      <DependentUpon>TraCuuDienEDI.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="TraCuuDienEDI.aspx.designer.cs">
      <DependentUpon>TraCuuDienEDI.aspx</DependentUpon>
    </Compile>
    <Compile Include="TraCuuDNN.aspx.cs">
      <DependentUpon>TraCuuDNN.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="TraCuuDNN.aspx.designer.cs">
      <DependentUpon>TraCuuDNN.aspx</DependentUpon>
    </Compile>
    <Compile Include="TraCuuLuong\Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="TraCuuLuong\Default.aspx.designer.cs">
      <DependentUpon>Default.aspx</DependentUpon>
    </Compile>
    <Compile Include="TrangThaiHangNhapASG.aspx.cs">
      <DependentUpon>TrangThaiHangNhapASG.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="TrangThaiHangNhapASG.aspx.designer.cs">
      <DependentUpon>TrangThaiHangNhapASG.aspx</DependentUpon>
    </Compile>
    <Compile Include="TrangThaiHangXuatASG.aspx.cs">
      <DependentUpon>TrangThaiHangXuatASG.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="TrangThaiHangXuatASG.aspx.designer.cs">
      <DependentUpon>TrangThaiHangXuatASG.aspx</DependentUpon>
    </Compile>
    <Compile Include="TrienKhaiVanBan.aspx.cs">
      <DependentUpon>TrienKhaiVanBan.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="TrienKhaiVanBan.aspx.designer.cs">
      <DependentUpon>TrienKhaiVanBan.aspx</DependentUpon>
    </Compile>
    <Compile Include="Truck\GiaBan.aspx.cs">
      <DependentUpon>GiaBan.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Truck\GiaBan.aspx.designer.cs">
      <DependentUpon>GiaBan.aspx</DependentUpon>
    </Compile>
    <Compile Include="Truck\GiaMua.aspx.cs">
      <DependentUpon>GiaMua.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Truck\GiaMua.aspx.designer.cs">
      <DependentUpon>GiaMua.aspx</DependentUpon>
    </Compile>
    <Compile Include="Truck\KhachHang.aspx.cs">
      <DependentUpon>KhachHang.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Truck\KhachHang.aspx.designer.cs">
      <DependentUpon>KhachHang.aspx</DependentUpon>
    </Compile>
    <Compile Include="Truck\TraCuu.aspx.cs">
      <DependentUpon>TraCuu.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Truck\TraCuu.aspx.designer.cs">
      <DependentUpon>TraCuu.aspx</DependentUpon>
    </Compile>
    <Compile Include="Truck\Tuyen.aspx.cs">
      <DependentUpon>Tuyen.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Truck\Tuyen.aspx.designer.cs">
      <DependentUpon>Tuyen.aspx</DependentUpon>
    </Compile>
    <Compile Include="TruyVanCPN.aspx.cs">
      <DependentUpon>TruyVanCPN.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="TruyVanCPN.aspx.designer.cs">
      <DependentUpon>TruyVanCPN.aspx</DependentUpon>
    </Compile>
    <Compile Include="TruyVanDNN.aspx.cs">
      <DependentUpon>TruyVanDNN.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="TruyVanDNN.aspx.designer.cs">
      <DependentUpon>TruyVanDNN.aspx</DependentUpon>
    </Compile>
    <Compile Include="TrangThaiHangXuat.aspx.cs">
      <DependentUpon>TrangThaiHangXuat.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="TrangThaiHangXuat.aspx.designer.cs">
      <DependentUpon>TrangThaiHangXuat.aspx</DependentUpon>
    </Compile>
    <Compile Include="TruyVanHangXuat.aspx.cs">
      <DependentUpon>TruyVanHangXuat.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="TruyVanHangXuat.aspx.designer.cs">
      <DependentUpon>TruyVanHangXuat.aspx</DependentUpon>
    </Compile>
    <Compile Include="QueryListInVoices.aspx.cs">
      <DependentUpon>QueryListInVoices.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="QueryListInVoices.aspx.designer.cs">
      <DependentUpon>QueryListInVoices.aspx</DependentUpon>
    </Compile>
    <Compile Include="TruyVanPO.aspx.cs">
      <DependentUpon>TruyVanPO.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="TruyVanPO.aspx.designer.cs">
      <DependentUpon>TruyVanPO.aspx</DependentUpon>
    </Compile>
    <Compile Include="UC\ThongBao.ascx.cs">
      <DependentUpon>ThongBao.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UC\ThongBao.ascx.designer.cs">
      <DependentUpon>ThongBao.ascx</DependentUpon>
    </Compile>
    <Compile Include="UC\TrangThaiNhanVien.ascx.cs">
      <DependentUpon>TrangThaiNhanVien.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UC\TrangThaiNhanVien.ascx.designer.cs">
      <DependentUpon>TrangThaiNhanVien.ascx</DependentUpon>
    </Compile>
    <Compile Include="UpdateFWD.aspx.cs">
      <DependentUpon>UpdateFWD.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UpdateFWD.aspx.designer.cs">
      <DependentUpon>UpdateFWD.aspx</DependentUpon>
    </Compile>
    <Compile Include="ViTri.aspx.cs">
      <DependentUpon>ViTri.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="ViTri.aspx.designer.cs">
      <DependentUpon>ViTri.aspx</DependentUpon>
    </Compile>
    <Compile Include="NhapTextNBA.aspx.cs">
      <DependentUpon>NhapTextNBA.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="NhapTextNBA.aspx.designer.cs">
      <DependentUpon>NhapTextNBA.aspx</DependentUpon>
    </Compile>
    <Compile Include="XemAnhHoaDonXuat.aspx.cs">
      <DependentUpon>XemAnhHoaDonXuat.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="XemAnhHoaDonXuat.aspx.designer.cs">
      <DependentUpon>XemAnhHoaDonXuat.aspx</DependentUpon>
    </Compile>
    <Compile Include="XemCanDIM.aspx.cs">
      <DependentUpon>XemCanDIM.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="XemCanDIM.aspx.designer.cs">
      <DependentUpon>XemCanDIM.aspx</DependentUpon>
    </Compile>
    <Compile Include="XuatBaoCao.aspx.cs">
      <DependentUpon>XuatBaoCao.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="XuatBaoCao.aspx.designer.cs">
      <DependentUpon>XuatBaoCao.aspx</DependentUpon>
    </Compile>
    <Compile Include="ZaloOA\GetZalo.aspx.cs">
      <DependentUpon>GetZalo.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="ZaloOA\GetZalo.aspx.designer.cs">
      <DependentUpon>GetZalo.aspx</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <Content Include="Site.Master" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Properties\licenses.licx" />
    <EmbeddedResource Include="Report\XtraReportCM.resx">
      <DependentUpon>XtraReportCM.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Report\XtraReportBCHX.resx">
      <DependentUpon>XtraReportBCHX.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Report\XtraReportDNN.resx">
      <DependentUpon>XtraReportDNN.cs</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="css\bootstrap-theme.css.map" />
    <None Include="css\bootstrap.css.map" />
    <Content Include="Dataset\DataSetHX.xsc">
      <DependentUpon>DataSetHX.xsd</DependentUpon>
    </Content>
    <None Include="Dataset\DataSetHX.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>DataSetHX.Designer.cs</LastGenOutput>
    </None>
    <Content Include="Dataset\DataSetHX.xss">
      <DependentUpon>DataSetHX.xsd</DependentUpon>
    </Content>
    <None Include="Dataset\DataSetSoLieu.xsc">
      <DependentUpon>DataSetSoLieu.xsd</DependentUpon>
    </None>
    <None Include="Dataset\DataSetSoLieu.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>DataSetSoLieu.Designer.cs</LastGenOutput>
    </None>
    <None Include="Dataset\DataSetSoLieu.xss">
      <DependentUpon>DataSetSoLieu.xsd</DependentUpon>
    </None>
    <None Include="Dataset\fonts\glyphicons-halflings-regular.eot">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <Content Include="Dataset\fonts\glyphicons-halflings-regular.ttf">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <None Include="Dataset\fonts\glyphicons-halflings-regular.woff">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <Content Include="packages.config" />
    <Content Include="Extensions\ckeditor\plugins\scayt\CHANGELOG.md" />
    <Content Include="Extensions\ckeditor\plugins\scayt\LICENSE.md" />
    <Content Include="Extensions\ckeditor\plugins\scayt\README.md" />
    <Content Include="Extensions\ckeditor\plugins\wsc\LICENSE.md" />
    <Content Include="Extensions\ckeditor\plugins\wsc\README.md" />
    <Content Include="Extensions\ckeditor\samples\old\htmlwriter\assets\outputforflash\outputforflash.fla" />
    <Content Include="Extensions\ckeditor\samples\toolbarconfigurator\font\config.json" />
    <Content Include="Extensions\ckeditor\samples\toolbarconfigurator\font\fontello.eot" />
    <Content Include="Extensions\ckeditor\samples\toolbarconfigurator\font\fontello.ttf" />
    <Content Include="Extensions\ckeditor\samples\toolbarconfigurator\font\fontello.woff" />
    <Content Include="Extensions\ckeditor\samples\toolbarconfigurator\lib\codemirror\LICENSE" />
    <Content Include="Extensions\ckeditor\skins\moono-lisa\readme.md" />
    <Content Include="KhaiBaoYTe\KhaiBaoYTe.Master" />
    <None Include="Properties\PublishProfiles\ALSE.pubxml" />
    <None Include="Properties\PublishProfiles\FolderProfile.pubxml" />
    <None Include="Properties\PublishProfiles\LJKENJI_PUBLISH.pubxml" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <WCFMetadata Include="Connected Services\" />
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>False</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>15961</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>http://localhost:15962/</IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\SSDT\Microsoft.Data.Tools.Schema.Sql.UnitTesting.targets" Condition="'$(VisualStudioVersion)' == ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\SSDT\Microsoft.Data.Tools.Schema.Sql.UnitTesting.targets" Condition="'$(VisualStudioVersion)' != ''" />
  <Import Project="..\packages\Microsoft.Bcl.Build.1.0.21\build\Microsoft.Bcl.Build.targets" Condition="Exists('..\packages\Microsoft.Bcl.Build.1.0.21\build\Microsoft.Bcl.Build.targets')" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\Microsoft.Bcl.Build.1.0.21\build\Microsoft.Bcl.Build.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.Bcl.Build.1.0.21\build\Microsoft.Bcl.Build.targets'))" />
  </Target>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>