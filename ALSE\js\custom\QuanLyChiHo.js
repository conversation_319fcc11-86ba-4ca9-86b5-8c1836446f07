var jsonData;
var ajaxGet;
var ajaxGet1;
var ajaxGet2;
var ajaxGet3;
var html_option;
var chiHo;
var arrayNCC;
var mawbhawb;
var listKhachHang;
var listNCC;
var fileData;
var arrTempData = {};
var arrUploadData = {};
var fileitem = "";
var count_item = 0;
var dntt_count = 0;
var themChiHoId = [];
var xoaChiHoId = [];
var kiemTraHoaDonData = [];
const chDateNow = new Date();
let checkedCount = 0; // Bộ đếm số lượng hóa đơn đã kiểm tra
let totalInvoices = 0; // Tổng số hóa đơn cần kiểm tra
let successCount = 0; // Bộ đếm số lượng hóa đơn đã kiểm tra thành công
let saiThanhTienCount = 0; // Bộ đếm số lượng hóa đơn sai thành tiền
let nguoiMuaKhongPhaiALSECount = 0; // Bộ đếm số lượng hóa đơn người mua không phải ALSE
let khongTonTaiSHDvaMSTCount = 0; // Bộ đếm số lượng hóa đơn không tồn tại SHĐ và MST
let hoadonDaCheckCount = 0; // Bộ đếm số lượng hóa đơn đã check
var usernameId = 0;
var chihos          = [];
var hdChuaDK        = 0;
var HdKhongNCC      = 0;
var HdKhongSoHD     = 0;
var Page            = 1;
var PageSize        = 50;
var TotalRecord     = 50;
$(document).ready(function () {
    fncLoad();
    fncClick();
    fncChange();
    fncModal();
    initializePagination(); // Khởi tạo pagination
    let dropArea = document.getElementById('drop-area');
    let dropAreaCheckHoaDon = document.getElementById('drop-area-check-hoa-don');

    // Ngăn chặn hành vi mặc định khi kéo thả
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        dropArea.addEventListener(eventName, preventDefaults, false);
        document.body.addEventListener(eventName, preventDefaults, false);
    });
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        dropAreaCheckHoaDon.addEventListener(eventName, preventDefaults, false);
        document.body.addEventListener(eventName, preventDefaults, false);
    });


    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    // Thay đổi kiểu dáng khi kéo tệp vào khu vực
    ['dragenter', 'dragover'].forEach(eventName => {
        dropArea.addEventListener(eventName, () => dropArea.classList.add('hover'), false);
    });

    ['dragleave', 'drop'].forEach(eventName => {
        dropArea.addEventListener(eventName, () => dropArea.classList.remove('hover'), false);
    });
    // Xử lý tệp khi thả
    dropArea.addEventListener('drop', handleDrop, false);
    //dropArea.addEventListener('click', () => document.getElementById('f_UploadImage').click());
    $(".input-chiho-search-clear").on("keydown", function(event) {
        if (event.key === "Enter") {
            $("#btn-chiho-search").click();
        }
        if (event.key === "Escape") {
            $("#btn-chiho-search-reset").click();
        }
    });
});
// End Hàm xử lý sự kiện nhập vào input

// Start Hàm load dữ liệu
function fncLoad() {
    document.title = "Quản Lý Chi Hộ";
    loadMainView();
    loadKH();
    loadNCC();
}
// End Hàm load dữ liệu

// Start Hàm xử lý sự kiện click
function fncClick() {
    // Hiển thị hóa đơn không có số hóa đơn
    $("#btn-chiho-notsohd").click(function () {
        ajaxGet4 = { "get1": "NotSoHD" };
        jsonData = JSON.stringify({ ajaxGet4 });
        fncSearch(jsonData);
    })

    // Hiển thị hóa đơn không có trong danh sách NCC
    $("#btn-chiho-notncc").click(function () {
        ajaxGet4 = { "get1": "NotNCC" };
        jsonData = JSON.stringify({ ajaxGet4 });
        fncSearch(jsonData);
    })

    $(".btn-chiho-taihoadon").click(function () {
        $("#modalTaiHoaDon").modal("show");
    });
    $("#btnDownloadHoaDon").click(function () {
        var awbBills = [];
        $("#multiTextBoxAWB").val().split('\n').forEach(line => {
            if (line.trim() !== '') {
                awbBills.push({ param1: line.trim() });
            }
        });
        if (awbBills.length > 0) {
            var jsonData = JSON.stringify({ listAwbBill: awbBills });
            $.ajax({
                type: "POST",
                url: "QuanLyChiHo.aspx/TaiHoaDon",
                data: jsonData,
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                async: false,
                success: function (responsive) {                    
                    
                    var taiHoaDonInfo = responsive.d;
                    var zipFileName = taiHoaDonInfo.ZipFileName;
                    var listTaiHoaDonDetail = taiHoaDonInfo.TaiHoaDonDetails;
                    var html_body_taihoadon = "";
                    var thd_codinhkem = "";
                    var thd_khongcodinhkem = "";
                    var thd_khongcoawbbill = "";
                    listTaiHoaDonDetail.forEach(function(taiHoaDonDetail){
                        if(taiHoaDonDetail.FileDinhKem != "" && taiHoaDonDetail.FileDinhKem > 0){
                            thd_codinhkem += taiHoaDonDetail.AWBBILL + "<br>";
                        }
                        if(taiHoaDonDetail.Id == ""){
                            thd_khongcoawbbill += taiHoaDonDetail.AWBBILL + "<br>";
                        }
                        if(taiHoaDonDetail.Id != "" && (taiHoaDonDetail.FileDinhKem == 0 || taiHoaDonDetail.FileDinhKem == "")){
                            thd_khongcodinhkem += taiHoaDonDetail.AWBBILL + "<br>";
                        }
                       
                    });
                    html_body_taihoadon += "<tr>";
                    html_body_taihoadon += "<td>Có đính kèm</td>";
                    html_body_taihoadon += "<td>" + thd_codinhkem + "</td>";
                    html_body_taihoadon += "</tr>";
                    html_body_taihoadon += "<tr>";
                    html_body_taihoadon += "<td>Không đính kèm</td>";
                    html_body_taihoadon += "<td>" + thd_khongcodinhkem + "</td>";
                    html_body_taihoadon += "</tr>";
                    html_body_taihoadon += "<tr>";
                    html_body_taihoadon += "<td>Không có AWB</td>";
                    html_body_taihoadon += "<td>" + thd_khongcoawbbill + "</td>";
                    html_body_taihoadon += "</tr>";
                    
                    $("#tbl-taihoadon tbody").empty().append(html_body_taihoadon);
                    $("#tbl-taihoadon").show();
                    if (zipFileName != "error") {
                        window.open("../DownloadCacheFile.aspx?FileName=" + zipFileName, "_blank");
                    } 
                    // else {
                    //     alert("Có lỗi xảy ra trong quá trình tải hóa đơn!");
                    // }
                },
                error: function (jqXHR, textStatus, errorThrown) {
                    console.error("Có lỗi xảy ra trong quá trình tải hóa đơn: " + textStatus + " - " + errorThrown);
                }
            });
        } else {
            alert("Vui lòng nhập ít nhất một số AWB để tải hóa đơn!");
        }
    });

    // Click show modal upload file
    // $("#tbl-chiho").on("click", "", function () {

    // })

    // Click duyệt thanh toán
    $("#btn-duyetthanhtoan").click(function () {
        var _Id = $(this).attr("attrID");
        ajaxGet = { "get": _Id };
        jsonData = JSON.stringify({ ajaxGet });

        swal.fire({
            title: "Xác nhận duyệt thanh toán",
            text: "Bạn có muốn duyệt thanh toán chi hộ không?",
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Đồng ý, duyệt thanh toán',
            cancelButtonText: 'Hủy'
        })
        .then((result) => {
            if (result.value) {
                $.ajax({
                    type: "POST",
                    url: "QuanLyChiHo.aspx/UpdateDuyetThanhToan",
                    data: jsonData,
                    contentType: "application/json; charset=utf-8",
                    dataType: "json",
                    async: false,
                    success: function (responsive) {
                        d = responsive.d;
                        if (d == "ok") {
                            $("#btn-duyetthanhtoan").hide();
                            $("#btn-duyetthanhtoan").attr("attrID", "");
                            swal.fire({
                                title: "Duyệt thanh toán thành công",
                                text: "hệ thống sẽ tự tải lại sau 2s",
                                type: 'success',
                                timer: 2000,
                            })
                            loadMainView();
                        }
        
                    },
                    error: function (responsive) {
                        alert("Có lỗi xảy ra! Vui lòng F5(Refresh)!");
                    }
                });
            }
        })
        
    })

    // Clicl show modal duyệt thanh toán 
    $("#tbl-chiho").on("click", ".btn-chiho-qr", function () {
        var _Id = $(this).attr("attrid");
        var _KihieuHD = $(this).attr("attrKiHieuHD");
        var _Thanhtien = $(this).attr("attrThanhToan");
        var _TenPhi = $(this).attr("TenPhi");
        var _NCC = $(this).attr("NCC");
        var _KhachHang = $(this).attr("KhachHang");
        var _AWBBILL = $(this).attr("AWBBILL");
        var _ThanhToanNCC = $(this).attr("ThanhToanNCC");

        $("#modalDuyetThanhToanChiHo").modal("show");
        $("#btn-duyetthanhtoan").attr("attrID", _Id);
        if(_ThanhToanNCC == "True"){
            $("#btn-duyetthanhtoan").hide();
        }else{
            $("#btn-duyetthanhtoan").show();
        }

        $(".text-amount").empty().append(fncTachPhanNghin(_Thanhtien))

        ajaxGet = { "get": _NCC };
        jsonData = JSON.stringify({ ajaxGet });

        $.ajax({
            type: "POST",
            url: "QuanLyChiHoNCC.aspx/reChiHoByHoaDonKH",
            data: jsonData,
            contentType: "application/json; charset=utf-8",
            dataType: "json",
            async: false,
            success: function (responsive) {
                d = responsive.d;
                //console.log(d)
                //var NoiDungCK = _TenPhi + " " + _NCC + " " + _KhachHang + " " + _AWBBILL;

                var NoiDungCK = "ALSE tt " + _AWBBILL;
                var NoiDungCK_Sanitized = removeDiacritics(NoiDungCK);
                $(".text-content").empty().append(NoiDungCK_Sanitized);
                $("#text-account").empty().append(d.SoTK);
                $("#text-name").empty().append(d.NguoiHuongThu);
               

                // Tạo URL cho API
                var url = `https://img.vietqr.io/image/${d.NganHang}-${d.SoTK}-compact2.jpg?amount=${_Thanhtien}&addInfo=${encodeURIComponent(NoiDungCK_Sanitized)}&accountName=${encodeURIComponent(d.NguoiHuongThu)}`;

                // Cập nhật ảnh QR Code trong modal
                $("#imgQRCode").attr("src", url);

               // document.getElementById("imgQRCode").src = "https://img.vietqr.io/image/" + d.bin + "-" + d.SoTK + "-compact.png?amount=" + _Thanhtien + "&addInfo=test&accountName=" + d.NguoiHuongThu + "";

            },
            error: function (responsive) {
                alert("Có lỗi xảy ra! Vui lòng F5(Refresh)!");
            }
        });

    });
    // Lưu chi hộ excel
    $("#btn-chiho-excel-luu").click(function () {
        var spreadsheet = $("#spreadsheet").data("kendoSpreadsheet");
        var data = spreadsheet.toJSON().sheets[0].rows;
        data = data.splice(1, data.length - 1);
        var DataInput = [];
        var DataInputWithoutNCC = [];
        var DataInputWithoutKhachHang = [];
        var DataInputWithoutAWB = [];
        var tempDataInput;
        var cells;
        // Loại hình
        var cell_LoaiHinh;
        // NCC
        var cell_NCC;
        // Khách hàng
        var cell_KhachHang;
        // Ký hiệu HĐ
        var cell_KiHieuHD;
        // Số HĐ
        var cell_SoHD;
        // Ngày HĐ
        var cell_NgayHd;
        // Người mua trên HĐ
        var cell_NguoiMuaHd;
        // Tên phí
        var cell_TenPhi;
        // AWB/BILL
        var cell_AWB;
        // Số tiền thanh toán
        var cell_ThanhTien;
        // Số tiền trước VAT
        var cell_SoTienThue;
        // Số DNTT
        var cell_SoDNTT;
        // Đã thanh toán NCC
        var cell_DaThanhToanNCC;
        // Ngày thanh toán NCC
        var cell_NgayThanhToanNCC;
        // Ghi chú
        var cell_GhiChu;
        var checkNCC = false;
        var checkKhachHang = false;
        var checkAWB = false;
        var html_body_canhbao_ncc = "";
        var html_body_canhbao_khachhang = "";
        var html_body_canhbao_awb = "";

        data.forEach(function (dataItem, dataIndex) {
            cells = "";
          
            cell_LoaiHinh = "";
            cell_NCC = "";
            cell_KhachHang = "";
            cell_KiHieuHD = "";
            cell_SoHD = "";
            cell_NgayHd = "";
            cell_NguoiMuaHd = "";
            cell_TenPhi = "";
            cell_AWB = "";
            cell_ThanhTien = "";
            cell_SoTienThue = "";
            cell_SoDNTT = "";
            cell_DaThanhToanNCC = "";
            cell_NgayThanhToanNCC = "";
            cell_GhiChu = "";
            checkNCC = false;
            checkKhachHang = false;
            checkAWB = false;
            cells = dataItem.cells;

            const cellMapping = {
                0: (value) => cell_LoaiHinh = value,
                1: (value) => cell_NCC = value,
                2: (value) => cell_KhachHang = value,
                3: (value) => cell_KiHieuHD = value,
                4: (value) => cell_SoHD = value,
                5: (value) => cell_NgayHd = fncConvertExcelDate(String(value).trim().replace(/ /g, '')),
                6: (value) => cell_NguoiMuaHd = value,
                7: (value) => cell_TenPhi = value,
                8: (value) => cell_AWB = value,
                9: (value) => cell_ThanhTien = value,
                10: (value) => cell_SoTienThue = value,
                11: (value) => cell_DaThanhToanNCC = value,
                12: (value) => cell_NgayThanhToanNCC = fncConvertExcelDate(String(value).trim().replace(/ /g, '')),
                13: (value) => cell_GhiChu = value
            };

            cells.forEach(function (cellItem, cellIndex) {
                const value = cells[cellIndex].value;
                if (value !== undefined && cellMapping[cellItem.index]) {
                    cellMapping[cellItem.index](value);
                }
            });
            cell_NCC =  cell_NCC.trim();
            var TenNguoiBan = fncTimNguoiBan(cell_NCC);
            tempDataInput = {
                    "Id": "",
                    "LoaiHinh": fncCleanString(cell_LoaiHinh),
                    "NCU": cell_NCC,
                    "KhachHang": fncCleanString(cell_KhachHang),
                    "KiHieuHD": fncCleanString(cell_KiHieuHD),
                    "SoHD": fncCleanString(cell_SoHD),
                    "NgayHd": cell_NgayHd,
                    "HoaDonKhach": fncCleanString(cell_NguoiMuaHd),
                    "TenNguoiBan": TenNguoiBan,
                    "PhiChungTuNhap": String(cell_TenPhi).trim(),
                    "AWBBILL": fncCleanString(cell_AWB),
                    "ThanhTien": fncCleanString(cell_ThanhTien),
                    "SoTruocThue": fncCleanString(cell_SoTienThue),
                    "ThanhToanNCC": fncCleanString(cell_DaThanhToanNCC),
                    "NgayThanhToanNCC": fncCleanString(cell_NgayThanhToanNCC),
                    "GhiChu": String(cell_GhiChu).trim(),
                    "NguonDuLieu": "EXCEL"
                }
                // check NCC có trong cơ sở dữ liệu không
                if(TenNguoiBan == ""){
                    checkNCC = true;
                    DataInputWithoutNCC.push(tempDataInput);    
                    html_body_canhbao_ncc += "<tr>";
                    html_body_canhbao_ncc += "<td>NCC</td>";
                    html_body_canhbao_ncc += "<td>" + cell_NCC + "</td>";
                    html_body_canhbao_ncc += "<td>" + "<i class='fas fa-times-circle text-danger'></i> Chưa có" + "</td>";
                    html_body_canhbao_ncc += "</tr>";
                }
                // check Khách hàng có trong cơ sở dữ liệu không
                var tempListKhachHang = listKhachHang.filter(function(khachHang){
                    return khachHang.LoaiHinh == cell_LoaiHinh && khachHang.KhachHang == cell_KhachHang;
                });
                if(tempListKhachHang.length == 0){
                    checkKhachHang = true;
                    DataInputWithoutKhachHang.push(tempDataInput);        
                    html_body_canhbao_khachhang += "<tr>";
                    html_body_canhbao_khachhang += "<td>Khách hàng</td>";
                    html_body_canhbao_khachhang += "<td>" + cell_KhachHang + "</td>";
                    html_body_canhbao_khachhang += "<td>" + "<i class='fas fa-times-circle text-danger'></i> Chưa có" + "</td>";
                    html_body_canhbao_khachhang += "</tr>";
                }
                // Check AWB/BILL có trong cơ sở dữ liệu không
                var awb = fncTimKiemAWBBill(cell_LoaiHinh, cell_KhachHang, cell_AWB);
                if(awb == null || awb == "" || awb == "0"){ 
                    checkAWB = true;
                    DataInputWithoutAWB.push(tempDataInput);
                    html_body_canhbao_awb += "<tr>";
                    html_body_canhbao_awb += "<td>"+ cell_LoaiHinh +"</td>";
                    html_body_canhbao_awb += "<td>" + cell_KhachHang + "</td>";
                    html_body_canhbao_awb += "<td>" + cell_AWB + "</td>";
                    html_body_canhbao_awb += "<td>" + "<i class='fas fa-times-circle text-danger'></i> Chưa có" + "</td>";
                    html_body_canhbao_awb += "</tr>";
                }
                if(!checkNCC && !checkKhachHang && !checkAWB){
                    DataInput.push(tempDataInput);
                }
            });
        
        // không cho lưu
        if(DataInputWithoutNCC.length > 0 || DataInputWithoutKhachHang.length > 0){
            html_body_canhbao = "<table class='table table-bordered table-hover'>";
            html_body_canhbao += "<thead>";
            html_body_canhbao += "<tr>";
            html_body_canhbao += "<th>Cảnh báo</th>";
            html_body_canhbao += "<th>Nội dung</th>";
            html_body_canhbao += "<th>Trạng thái</th>";
            html_body_canhbao += "</tr>";
            html_body_canhbao += "</thead>";
            html_body_canhbao += "<tbody>";
            
            html_body_canhbao += html_body_canhbao_ncc;
            html_body_canhbao += html_body_canhbao_khachhang;
            html_body_canhbao += "</tbody>";
            html_body_canhbao += "</table>";
            swal.fire({
                title: "Kiểm tra lại dữ liệu NCC/Khách hàng bên dưới chưa có",
                html: html_body_canhbao,
                type: 'warning',
            })
        }else{
            if(DataInputWithoutAWB.length > 0){
                html_body_canhbao = "<table class='table table-bordered table-hover'>";
                html_body_canhbao += "<thead>";
                html_body_canhbao += "<tr>";
                html_body_canhbao += "<th>Loại hình</th>";
                html_body_canhbao += "<th>Khách hàng</th>";
                html_body_canhbao += "<th>AWB/BILL</th>";
                html_body_canhbao += "<th>Trạng thái</th>";
                html_body_canhbao += "</tr>";
                html_body_canhbao += "</thead>";
                html_body_canhbao += "<tbody>";
                html_body_canhbao += html_body_canhbao_awb;
                html_body_canhbao += "</tbody>";
                html_body_canhbao += "</table>";
                swal.fire({
                    title: "Có " + DataInputWithoutAWB.length + " AWB/BILL không có trong cơ sở dữ liệu",
                    html: "Bạn có muốn lưu không? <br>" + html_body_canhbao,
                    icon: "warning",
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'Đồng ý, lưu',
                    cancelButtonText: 'Hủy'
                })
                .then((result) => {
                    if (result.value) {
                        DataInput.push(...DataInputWithoutAWB);
                        fncLuuExcel(DataInput);
                    }
                        
                })
            }else{
                fncLuuExcel(DataInput);
            }
        }
       
      
        

        

    })
    // End Hàm xử lý sự kiện click
    // Start Hàm lưu excel
    function fncLuuExcel(DataInput){
        console.log(DataInput);
        $.ajax({
            type: "POST",
            url: "QuanLyChiHo.aspx/InsertChiHoExcel",
            data: JSON.stringify({DataInput }),
            contentType: "application/json; charset=utf-8",
            dataType: "json",
            async: false,
            success: function (responsive) {
                d = responsive.d;
                //console.log(d)
                if (d == "ok") {
                    loadMainView();
                    $("#modalQuanLyChiHoExcel").modal("hide");
                    swal.fire({
                        title: "Lưu chi hộ thành công",
                        text: "hệ thống sẽ tự tải lại sau 2s",
                        type: 'success',
                        timer: 2000,
                    })
                }
            },
            error: function (responsive) {
                alert("Có lỗi xảy ra! Vui lòng F5(Refresh)!");
            }
        })
    }   
    // End Hàm lưu excel
    // Start Hàm tìm kiếm AWB/BILL
    function fncTimKiemAWBBill(ncc, khachhang, awb_bill){
        var ajaxGet4 = { "get1": ncc, "get2": khachhang, "get3": awb_bill, "get4": "" };
        var jsonData = JSON.stringify({ ajaxGet4 });
        var kq = "0";
        $.ajax({
            type: "POST",
            url: "QuanLyChiHo.aspx/TimKiemAWBBill",
            data: jsonData,
            contentType: "application/json; charset=utf-8",
            dataType: "json",
            async: false,
            success: function (responsive) {
                d = responsive.d;
                kq = d;
            }
        })
        return kq;
    }
    // End Hàm tìm kiếm AWB/BILL
    // Show excel chi hộ
    $(".btn-chiho-kehoach-excel").click(function () {
        $("#modalQuanLyChiHoExcel").modal(
            {
                show: true,
                backdrop: "static",
                keyboard: false
            });
        $("#spreadsheet").empty();
        $("#spreadsheet").kendoSpreadsheet({
            columns: 2,
            rows: 500,
            toolbar: false,
            sheetsbar: false,
        });
        var spreadsheet = $("#spreadsheet").data("kendoSpreadsheet");
        var sheet = spreadsheet.activeSheet();
        sheet.range(kendo.spreadsheet.SHEETREF).clear();
        $(window).trigger("resize");
        spreadsheet.fromJSON({
            sheets: [{
                name: "KeHoach",
                //mergedCells: [
                //    "A1:G1"
                //],
                rows: [{
                    height: 40,
                    cells: [
                        { value: "Loại hình", textAlign: "center", verticalAlign: "center", bold: true, wrap: true, enable: false }
                        , { value: "NCC", textAlign: "center", verticalAlign: "center", bold: true, wrap: true, enable: false }
                        , { value: "Khách hàng", textAlign: "center", verticalAlign: "center", bold: true, wrap: true, enable: false }
                        , { value: "Ký hiệu HĐ", textAlign: "center", verticalAlign: "center", bold: true, wrap: true, enable: false }
                        , { value: "Số HĐ", textAlign: "center", verticalAlign: "center", bold: true, wrap: true, enable: false }
                        , { value: "Ngày HĐ", textAlign: "center", verticalAlign: "center", bold: true, wrap: true, enable: false }
                        , { value: "Người mua trên HĐ", textAlign: "center", verticalAlign: "center", bold: true, wrap: true, enable: false }
                        , { value: "Tên phí", textAlign: "center", verticalAlign: "center", bold: true, wrap: true, enable: false }
                        , { value: "AWB/BILL", textAlign: "center", verticalAlign: "center", bold: true, wrap: true, enable: false }
                        , { value: "Số tiền thanh toán", textAlign: "center", verticalAlign: "center", bold: true, wrap: true, enable: false }
                        , { value: "Số tiền trước VAT", textAlign: "center", verticalAlign: "center", bold: true, wrap: true, enable: false }
                        , { value: "Đã thanh toán NCC", textAlign: "center", verticalAlign: "center", bold: true, wrap: true, enable: false }
                        , { value: "Ngày thanh toán NCC", textAlign: "center", verticalAlign: "center", bold: true, wrap: true, enable: false } 
                        , { value: "Ghi chú", textAlign: "center", verticalAlign: "center", bold: true, wrap: true, enable: false }
                    ]
                }],
                columns: [
                    {// Loại hình
                        width: 100
                    },
                    {// NCC
                        width: 100
                    },
                    {// Khách hàng
                        width: 100
                    },
                    {// Ký hiệu HĐ
                        width: 100
                    },
                    {// Số HĐ   
                        width: 100
                    },
                    {// Ngày HĐ
                        width: 100
                    },
                    {// Người mua trên HĐ
                        width: 100
                    },
                    {// Tên phí
                        width: 200
                    },
                    {// AWB/BILL
                        width: 100
                    },
                    {// Số tiền thanh toán
                        width: 100
                    },
                    {// Số tiền trước VAT
                        width: 100
                    },
                    {// Đã thanh toán NCC
                        width: 100
                    },
                    {// Ngày thanh toán NCC
                        width: 100
                    },
                    {//Ghi chú
                        width: 300
                    }
                ]
            }]
        });
    })      
    // End Show excel chi hộ
    // Start Chi hộ xóa
    $("#tbl-chiho").on("click", ".btn-chiho-xoa", function () {
        var Id = $(this).attr("attrID");
        var Xoa_SoDeNghiThanhToan = $(this).attr("sodenghithanhtoan");
        var Xoa_AWBBILL = $(this).attr("AWBBILL");
        var Xoa_Text= "Bạn có muốn xóa đơn chi hộ này không?";
        if (Xoa_SoDeNghiThanhToan != '') {
            Xoa_Text += " Số đề nghị thanh toán: " + Xoa_SoDeNghiThanhToan;
        }
        if (Xoa_AWBBILL != '') {
            Xoa_Text += " AWBBILL: " + Xoa_AWBBILL;
        }
        swal.fire({
            title: "Xác nhận xóa",
            text: Xoa_Text,
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Đồng ý, xóa chi hộ',
            cancelButtonText: 'Hủy'
        })
        .then((result) => {
            if (result.value) {
                ajaxGet = { "get": Id };
                jsonData = JSON.stringify({ ajaxGet });

                $.ajax({
                    type: "POST",
                    url: "QuanLyChiHo.aspx/DeleteChiHo",
                    data: jsonData,
                    contentType: "application/json; charset=utf-8",
                    dataType: "json",
                    async: false,
                    success: function (responsive) {
                        d = responsive.d;
                        if (d == "ok") {
                            loadMainView();
                            swal.fire({
                                title: "Xóa chi hộ thành công",
                                text: "hệ thống sẽ tự tải lại sau 2s",
                                type: 'success',
                                timer: 2000,
                            })
                        }
                    },
                    error: function (responsive) {
                        alert("Có lỗi xảy ra! Vui lòng F5(Refresh)!");
                    }
                });
            }
        })
    })
    // End Chi hộ xóa
    // Start Lưu chi hộ 
    $("#btn-luu-chiho").click(function () {
        if ($("#input-chiho-ncu").val() === "") {
            swal.fire({
                title: "Vui lòng chọn NCC",
                type: 'warning',
            })
        } else {
            InsertUpdateChiHo("");
        }
    });
    // End Lưu chi hộ 
    // Start Cập nhật chi hộ
    $("#btn-capnhat-chiho").click(function () {
        if ($("#input-chiho-ncu").val() === "") {
            swal.fire({
                title: "Vui lòng chọn NCC",
                type: 'warning',
            })
        }
        else {
            InsertUpdateChiHo($(this).attr("attrid"));

        }
    });
    // End Cập nhật chi hộ
    // Start Sửa chi hộ
    $("#tbl-chiho").on("click", ".btn-chiho-sua", function () {
        var chiHoId= $(this).attr("attrID");
        var chiHoAWBBILL= $(this).attr("AWBBILL");
        $("#myModalViewChiHo").modal({ backdrop: 'static' }, "show");
        $("#btn-capnhat-chiho").attr("attrid", chiHoId)
        $("#h4-chiho-view-tieude").empty().append("Cập nhật chi hộ - " + chiHoAWBBILL);
        $("#btn-luu-chiho").hide();
        $("#btn-capnhat-chiho").show();

        ajaxGet = { "get": chiHoId };
        jsonData = JSON.stringify({ ajaxGet });

        $.ajax({
            type: "POST",
            url: "QuanLyChiHo.aspx/reChiHoById",
            data: jsonData,
            contentType: "application/json; charset=utf-8",
            dataType: "json",
            async: false,
            success: function (responsive) {
                d = responsive.d;
                console.log(d);
                $("#input-chiho-ncu").val(d.NCU);
                $("#select-chiho-loaihinh").val(d.LoaiHinh);
                $("#input-chiho-ngaychuyenkhoan").val(convertDate(d.NgayCK)[1]);
                $("#input-chiho-khachhang").val(d.KhachHang);
                $("#input-chiho-awbbill").val(d.AWBBILL);
                $("#input-chiho-check").val(d.Check_ChiHo);
                $("#input-chiho-kihieuhd").val(d.KiHieuHD);
                $("#input-chiho-sohd").val(d.SoHD);
                $("#input-chiho-ngayhd").val(convertDate(d.NgayHD)[1]);
                $("#input-chiho-tennguoiban").val(d.TenNguoiBan);
                $("#input-chiho-sotienthue").val(fncTachPhanNghin(d.SoTruocThue));
                $("#input-chiho-thanhtien").val(fncTachPhanNghin(d.ThanhTien));
                $("#input-chiho-tt-dck").val(d.TrangThaiDoiChieuKhach);
                $("#input-chiho-tt-dntt").val(d.TrangThaiDNTT);
                $("#input-chiho-idnhap").val(d.IdNhap);
                $("#input-chiho-ghichu").val(d.GhiChu);
                $("#input-chiho-phichungtunhap").val(d.PhiChungTuNhap);
                $("#input-chiho-sodenghithanhtoan").val(d.SoDeNghiThanhToan);
                $("#checkboxSuccess-dathanhtoanncc").prop("checked", d.ThanhToanNCC == "True" ? true : false);
                var options = [
                    { value: "ALSE", text: "ALSE" },
                    { value: d.KhachHang, text: d.KhachHang },
                    { value: "OTHER", text: "OTHER" }
                ];

                var html_option_nguoimua = options.map(option => {
                    let selected = (option.value === d.HoaDonKhach) ? "selected" : "";
                    return `<option value="${option.value}" ${selected}>${option.text}</option>`;
                }).join('');
                
                $("#input-chiho-nguoimua").empty().append(html_option_nguoimua);
                if(d.ThanhToanNCC == "False"){
                    $("#input-chiho-ngaythanhtoanncc").prop("disabled", true);
                    $("#input-chiho-ngaythanhtoanncc").val("");
                    
                    $('#checkboxSuccess-dathanhtoanncc').parent('label').contents().filter(function() {
                        return this.nodeType === 3; // Chọn các node văn bản
                    }).last().replaceWith('Đã thanh toán NCC');
                }else{
                    $("#input-chiho-ngaythanhtoanncc").prop("disabled", false);
                    $("#input-chiho-ngaythanhtoanncc").val(convertDate(d.NgayThanhToanNCC)[1]);
                    $('#checkboxSuccess-dathanhtoanncc').parent('label').contents().filter(function() {
                        return this.nodeType === 3; // Chọn các node văn bản
                    }).last().replaceWith(d.NguoiDuyetThanhToanNCC+' đã thanh toán NCC');
                }
                

            },
            error: function (responsive) {
                alert("Có lỗi xảy ra! Vui lòng F5(Refresh)!");
            }

        });
        // load file dinh kem
        fncLoadFileDinhKem(chiHoId);

    });
    // End Sửa chi hộ
    // Start Tải file đính kèm
    $("#tbl-chiho").on("click", ".btn-chiho-dinhkem", function () {
        var chiho_dinhkem = $(this);
        var chonFile = document.getElementById("f_UploadImage");
        chonFile.click();
        //console.log("click");   
        chonFile.onchange = function(){ 
            if(this.files.length > 0){  
                //console.log($(this).parents(".td-chucnang"));
                chiho_dinhkem.parents(".td-chucnang").find(".btn-chiho-sua").click();
            }
            // console.log("onchange");
        }
        
    })
    // End Tải file đính kèm
    // Start Show modal lưu cho hộ kế hoạch
    $(".btn-chiho-kehoach").click(function () {
        $("#myModalViewChiHo").modal({ backdrop: 'static' }, "show");
        $("#h4-chiho-view-tieude").empty().append("Thêm mới chi hộ");
        $("#btn-luu-chiho").show();
        $("#btn-capnhat-chiho").hide();
        // ngày hiện tại deploy 20/09
        
        $("#input-chiho-ngaychuyenkhoan").datepicker("setDate", new Date(chDateNow.getFullYear(), chDateNow.getMonth() + 1, 0));
        $("#input-chiho-ngayhd").datepicker();
        $("#input-chiho-ngaythanhtoanncc").datepicker();

        $("#input-chiho-khachhang").prop('disabled', true);
        $("#input-chiho-awbbill").prop('disabled', true);
        if ($("#tbl-upload-imgzone tbody tr").length === 0) {
            $("#tbl-upload-imgzone tbody").append("<tr><td colspan=\"4\" class=\"text-center\">Không có file upload</td></tr>");
        }


    });
    // End Show modal lưu cho hộ kế hoạch
    // Start delete ảnh trên server 
    ///
    $("#myModalViewChiHo").on("click", "#a-dinhkem-xoa", function () {
        if (confirm("Bạn có chắc chắn muốn xóa tài liệu này không? \r\nHành động này không thể hoàn tác! \r\nTên tài liệu: " + $(this).closest("tr").attr("filename"))) {
            //$("#div-wait").show();
            var xoa_folder = $(this).closest("tr").attr("folder");
            var ajaxGet2 = { "get1": xoa_folder, "get2": $(this).closest("tr").attr("filename") };
            jsonData = JSON.stringify({ ajaxGet2 });
            $.ajax({
                type: "POST",
                url: "QuanLyChiHo.aspx/DeleteFile",
                data: jsonData,
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                async: false,
                success: function (responsive) {
                    d = responsive.d;
                    //fncModalSua($("#myModalViewThanhToan").attr("id-thanhtoan"), $("#myModalViewThanhToan").attr("loai-thanhtoan"));
                    fncLoadFileDinhKem(xoa_folder);
                    alert("Xóa thành công");

                },
                error: function () {
                    alert("Đã có lỗi trong quá trình xóa file!\r\nVui lòng tải lại trang(F5)!\r\nNếu sự cố lặp lại xin liên hệ nhân viên IT");
                }
            }).done(function () {
                //$("#div-wait").hide();
            })
        }
    })
    // End delete ảnh trên server 
    // Start Tải ảnh trên server về máy tính
    $("#myModalViewChiHo").on("click", "#a-dinhkem-taixuong", function () {
        window.open("../DownloadFile.aspx?Root=ChiHo&Folder=" + $(this).closest("tr").attr("folder") + "&FileName=" + $(this).closest("tr").attr("filename"));
    })
    // End Tải ảnh trên server về máy tính
    // Start delete ảnh
    $("#myModalViewChiHo").on("click", "#a-upload-delete-all", function () {
        arrTempData = {};
        arrUploadData = {};
        $("#tbl-upload-imgzone tbody tr").remove();
        fncResetProcessBar();
    })
    // End delete ảnh
    // Start Show ảnh

    // Show ảnh

    $("#myModalViewChiHo").on("change", "#f_UploadImage", function (e) {
        fncResetProcessBar();
        html_imgzone = "";
        var file, img;
        count_item = $("#tbl-upload-imgzone tbody tr.tr-upload-chuaupload").length + $("#tbl-upload-imgzone tbody tr.tr-upload-daupload").length;
        // console.log("chua upload: " + $("#tbl-upload-imgzone tbody tr.tr-upload-chuaupload").length);
        // console.log("da upload: " + $("#tbl-upload-imgzone tbody tr.tr-upload-daupload").length);
        // console.log("total: " + count_item);
        $.each(e.target.files, function (item, val) {
            if (val.size < 10000000) {
                var fileExtension = val.name.split('.').pop();
                var awb = $("#input-chiho-awbbill").val();
                var ncc = $("#input-chiho-ncu").val();
                var khachhang = $("#input-chiho-khachhang").val();
                var sohoadon = $("#input-chiho-sohd").val();
                var newFileName = awb + "-" + ncc + "-" + khachhang + "-" + sohoadon + "-" + String(count_item + 1) + "." + fileExtension;
                var newFile = new File([val], newFileName, { type: val.type });
                arrUploadData["file" + count_item] = newFile;
                arrTempData["file" + count_item] = val;
                tmppath = URL.createObjectURL(val);
                html_imgzone += "<tr class=\"tr-upload-chuaupload\">";
                html_imgzone += "<td>" + "<span class=\"span-upload-trangthai label label-default\">" + "Chưa upload" + "</span>" + "</td>";
                //html_imgzone += "<td>" + "<img class=\"img-pre-upload\" src=\"" + tmppath + "\"  alt=\"Photo\" />" + "</td>";
                html_imgzone += "<td>" + newFileName + "</td>";
                html_imgzone += "<td>" + fncConvertSize(val.size) + "</td>";
                html_imgzone += "<td>" + "<a class=\"btn btn-danger btn-sm btn-upload-delete\" fileitem=\"file" + count_item + "\" ><i class=\"glyphicon glyphicon-trash\"></i> Xóa</a>" + "</td>";
                html_imgzone += "</tr>";
                count_item += 1;
            }
        })
        $("#tbl-upload-imgzone").append(html_imgzone);

         // Đặt lại giá trị của input để kích hoạt sự kiện change lần sau
         e.target.value = '';
         
        $("#tbl-upload-imgzone").on("click", ".btn-upload-delete", function () {
            event.stopPropagation();
            fileitem = $(this).attr("fileitem");
            delete arrTempData[fileitem];
            delete arrUploadData[fileitem];
            $(this).closest("tr").remove();
            fncResetProcessBar();
        })
    })
    // End Show ảnh

    // Start Highlight chi hộ
    $("#tbl-chiho").on("click", "tr td", function () {
        $(".tr-highlight").removeClass("tr-highlight");
        $(this).closest("tr").addClass("tr-highlight");
    })
    // End Highlight chi hộ
    // Start Tạo đề nghị thanh toán
    $(".btn-chiho-taodntt").click(function () { 
        dntt_count = 0;
        $("#btnDuyetDNTT").hide();
        $("#btnTaiHDDNTT").hide();
        $("#btnDuyetDNTT").attr("dnttId", "");
        $("#btnTaiHDDNTT").attr("dnttId", "");
        $("#btnInDNTT").attr("dnttId", "");
        $("#btnTaiDNTTExcel").attr("dnttId", "");
        $("#btnSaveDNTT").attr("dnttId", "");
        $("#modalTaoDNTTLabel").text("Tạo đề nghị thanh toán");
        fncDaDuyetDNTT("False", "", "");
        $("#modalTaoDNTT").modal({ backdrop: 'static' }, "show");
        $("#input-dntt-nguoidenghi").val($("#username").text().trim());
    });
    // End Tạo đề nghị thanh toán
    // Start Check chi hộ
    $("#tbl-dntt-chiho tbody").on("click", ".td-cb-dntt-child", function () {
        var isChecked = $(this).prop("checked");
        var chihoId = $(this).attr("tr-attr-id");
        var sohoadon = $(this).attr("tr-attr-sohoadon");
        var kihieu = $(this).attr("tr-attr-kihieu");
        var awbbill = $(this).attr("tr-attr-awbbill");
        var ngayhd = $(this).attr("tr-attr-ngayhd");
        var tennguoiban = $(this).attr("tr-attr-tennguoiban");
        var phichungtunhap = $(this).attr("tr-attr-phichungtunhap");
        var thanhtien = $(this).attr("tr-attr-thanhtien");
        var khachhang = $(this).attr("tr-attr-khachhang");
        const chihoIdArray = chihoId.split(',');
        if (isChecked) {
            // Thêm từng phần tử vào mảng themChiHoId
            chihoIdArray.forEach(id => {
                dntt_count += 1;
                themChiHoId.push(id.trim());
            });
            var html = "<tr id=\"tr-dntt-" + sohoadon + "\">";
            html += "<td>" + dntt_count + "</td>";
            html += "<td>" + kihieu + "</td>";
            html += "<td>" + sohoadon + "</td>";
            html += "<td>" + ngayhd + "</td>";
            html += "<td>" + tennguoiban + "</td>";
            html += "<td>" + phichungtunhap+ " " + khachhang + " " + awbbill + "</td>";
            html += "<td>" + fncTachPhanNghin(thanhtien) + "</td>";
            html += "<td>" + "<a href=\"#\" class=\"color-red btn-dntt-xoa\" chihoIdCount=\"" + chihoIdArray.length + "\" attrID=\"" + chihoId + "\" attrSohoadon=\"" + sohoadon + "\">Xóa</a>" + "</td>";
            html += "</tr>";
            $("#tbl-dntt-chitiet tbody").append(html);
            
            
        } else {
            $("#tr-dntt-" + sohoadon).remove();
            dntt_count -= chihoId.split(',').length;
            $("#tbl-dntt-chitiet tbody tr").each(function(index) {
                $(this).find("td:first-child").text(index + 1); // đánh lại số thứ tự
            });
            chihoIdArray.forEach(idDeleted => {
                themChiHoId = themChiHoId.filter(id => id !== idDeleted);
            });
            
        }
    });
    // End Check chi hộ
    // Start Xóa chi hộ
    $("#tbl-dntt-chitiet").on("click", ".btn-dntt-xoa", function () {
        var xoa_chihoId = $(this).attr("attrID");
        var xoa_sohoadon = $(this).attr("attrSohoadon");
        $("#td-cb-dntt-" + xoa_sohoadon).prop("checked", false); 
        $(this).closest("tr").remove();
        xoa_chihoId.split(',').forEach(idDelete => {
            themChiHoId = themChiHoId.filter(id => id !== idDelete);
        });
        
        dntt_count -= xoa_chihoId.split(',').length;
        $("#tbl-dntt-chitiet tbody tr").each(function(index) {
            $(this).find("td:first-child").text(index + 1);
        });
        if($("#tbl-dntt-chitiet tbody tr").length == 0){
            $("#btnSaveDNTT").hide();
        }else{
            $("#btnSaveDNTT").show();
        }
    });
    // End Xóa chi hộ
    // Start Xóa chi hộ trong đề nghị thanh toán
    $("#tbl-dntt-chitiet").on("click", ".btn-dntt-xoadb", function () {
        var chihoId = $(this).attr("attrID");
        var xoa_dntt_sohoadon = $(this).attr("sohoadon");
        var row_dntt = $(this).closest("tr");
        var row_diengiai = $(this).attr("diengiai");
        swal.fire({
            title: "Xác nhận xóa chi hộ",
            html: "Bạn có chắc chắn muốn xóa chi hộ này không? <br>Số hóa đơn: " + xoa_dntt_sohoadon + "<br> " + row_diengiai,
            type: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Đồng ý, xóa chi hộ',
            cancelButtonText: 'Hủy'
        }).then((result) => {
            if (result.value) {
                row_dntt.remove();
                chihoId.split(',').forEach(id => {
                    xoaChiHoId.push(id.trim());
                });
                dntt_count -= chihoId.split(',').length;
            }
            if($("#tbl-dntt-chitiet tbody tr").length == 0){
                $("#btnDuyetDNTT").hide();
                $("#btnTaiHDDNTT").hide();
                $("#btnLuuDNTT").hide();
            }else{
                $("#btnLuuDNTT").show();
                $("#btnTaiHDDNTT").show();
            }
        });
        
    });
    // End Xóa chi hộ trong đề nghị thanh toán
    // Start Lưu đề nghị thanh toán
    $("#btnSaveDNTT").click(function () {
        InsertUpdateDNTT($(this).attr("dnttId"));
    });
    // End Lưu đề nghị thanh toán
    // Start Xem đề nghị thanh toán
    $("#tbl-chiho").on("click", ".td-view-dntt", function () {
        var dnttId = $(this).attr("dnttId");
        if(dnttId != "" && dnttId != null){
            $("#btnInDNTT").attr("dnttId", dnttId);
            $("#btnInDNTT").show();
            $("#btnTaiDNTTExcel").attr("dnttId", dnttId);
            $("#btnTaiDNTTExcel").show();
            $("#btnTaiHDDNTT").attr("dnttId", dnttId);
            $("#btnTaiHDDNTT").show();
            $("#btnDuyetDNTT").attr("dnttId", dnttId);
            $("#btnDuyetDNTT").show();
            $("#modalTaoDNTT").modal({ backdrop: 'static' }, "show");
            $("#btnSaveDNTT").attr("dnttId", dnttId);
            //$("#input-dntt-id").val(dnttId);
            $("#modalTaoDNTTLabel").text("Sửa đề nghị thanh toán: " + dnttId);
            // Gọi ajax lấy dữ liệu DNTT
            ajaxGet = { "get": dnttId };
            jsonData = JSON.stringify({ ajaxGet });
            $.ajax({
                type: "POST",
                url: "QuanLyChiHo.aspx/ReDNTTId",
                data: jsonData,
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                async: false,
                success: function (responsive) {
                    d = responsive.d;
                    console.log(d);
                    fncDaDuyetDNTT(d.DaDuyet, d.NguoiDuyet, d.NgayDuyet);
                    // đổ data vào tbl-dntt-chitiet
                    $("#tbl-dntt-chitiet tbody").empty();
                    $("#input-dntt-noidungthanhtoan").val(d.NoiDungThanhToan);
                    //$("#input-dntt-ghichu").val(d.GhiChu);
                    $("#input-dntt-nguoidenghi").val(d.TenNguoiTao);
                    dntt_count = 1;
                    let tempDnttCtId = "";
                    let tempDnttCtSoHoaDon = "";
                    let tempDnttCtKhachHang = [];
                    let tempDnttCtAwbBill = [];
                    let tempDnttCtTenPhi = []; // Phí chứng từ nhập
                    let tempDnttCtSoTienSauVat = 0; // Thành tiền
                    $.each(d.DnttChiTiets, function (key, val) {
                        if(tempDnttCtSoHoaDon != val.SoHD || val.SoHD == ""){
                            tempDnttCtId = val.ChiHoId;
                            tempDnttCtSoHoaDon = val.SoHD;
                            tempDnttCtKhachHang = [];
                            tempDnttCtAwbBill = [];
                            tempDnttCtTenPhi = [];
                            tempDnttCtKhachHang.push(val.KhachHang);
                            tempDnttCtAwbBill.push(val.AWBBILL);
                            tempDnttCtTenPhi.push(val.TenPhi);
                            tempDnttCtSoTienSauVat = Number(val.ThanhTien);

                            var chihoId = val.ChiHoId;
                            var sohoadon = val.SoHD;
                            var kihieu = val.KiHieuHD;
                            var awbbill = val.AWBBILL;
                            var ngayhd = val.NgayHD;
                            var tennguoiban = val.TenNguoiBan;
                            var phichungtunhap = val.TenPhi;
                            var thanhtien = val.ThanhTien;
                            var khachHang = val.KhachHang;
                            var diengiai = phichungtunhap + " " + khachHang + " " + awbbill;
                            var html = "<tr id=\"tr-dntt-" + sohoadon + "\">";
                            html += "<td>" + dntt_count + "</td>";
                            html += "<td>" + kihieu + "</td>";
                            html += "<td id=\"td-dntt-ct-sohoadon-" + sohoadon + "\">" + sohoadon + "</td>";
                            html += "<td id=\"td-dntt-ct-ngayhd-" + sohoadon + "\">" + convertDate(ngayhd)[1] + "</td>";
                            html += "<td id=\"td-dntt-ct-tennguoiban-" + sohoadon + "\">" + tennguoiban + "</td>";
                            html += "<td id=\"td-dntt-ct-phichungtunhap-" + sohoadon + "\">" + diengiai + "</td>";
                            html += "<td id=\"td-dntt-ct-thanhtien-" + sohoadon + "\">" + fncTachPhanNghin(thanhtien) + "</td>";
                            html += "<td>" + "<a href=\"#\" id=\"btn-dntt-ct-xoa-" + sohoadon + "\" class=\"color-red btn-dntt-xoadb\" sohoadon=\"" + sohoadon + "\" attrID=\"" + chihoId + "\" diengiai=\"" + diengiai + "\">Xóa</a>" + "</td>";
                            html += "</tr>";
                            $("#tbl-dntt-chitiet tbody").append(html);
                            dntt_count += 1;
                        }else{
                            tempDnttCtId += "," + val.ChiHoId;
                            $("#btn-dntt-ct-xoa-" + val.SoHD).attr("attrID", tempDnttCtId);
                            if(!tempDnttCtKhachHang.includes(val.KhachHang)){
                                tempDnttCtKhachHang.push(val.KhachHang);
                            }
                            if(!tempDnttCtAwbBill.includes(val.AWBBILL)){
                                tempDnttCtAwbBill.push(val.AWBBILL);
                            }
                            if(!tempDnttCtTenPhi.includes(val.TenPhi)){
                                tempDnttCtTenPhi.push(val.TenPhi);
                            }
                            let tempDienGiai = tempDnttCtTenPhi.join("\n") + " " + tempDnttCtKhachHang.join("-") + " " + tempDnttCtAwbBill.join("\n");
                            $("#td-dntt-ct-phichungtunhap-" + val.SoHD).text(tempDienGiai);
                            $("#btn-dntt-ct-xoa-" + val.SoHD).attr("diengiai", tempDienGiai);
                            tempDnttCtSoTienSauVat += Number(val.ThanhTien);
                            $("#td-dntt-ct-thanhtien-" + val.SoHD).text(fncTachPhanNghin(tempDnttCtSoTienSauVat));
                        }
                    });
                }
            })

        }
        
    });
    // End Xem đề nghị thanh toán
    // Start In đề nghị thanh toán
    $("#modalTaoDNTT").on("click", "#btnInDNTT", function () {
        var dnttId = $(this).attr("dnttId");
        var url = "PdfViewer.aspx?FolderName=ChiHo&FileName=DNTT/"+dnttId+"/DNTT" + dnttId + ".pdf";
        window.open(url, '_blank');
    });
    // End In đề nghị thanh toán
    // Start Tải file excel
    $("#btnTaiDNTTExcel").click(function () {
        var dnttId = $(this).attr("dnttId");
        var url = "../DownloadFile.aspx?Root=ChiHo&Folder=DNTT/" + dnttId + "&FileName=DNTT" + dnttId + ".xlsx";
        window.open(url, '_blank');
    });
    // End Tải file excel

    // Start tìm kiesm không có file đính kèm
    $("#btn-chiho-search-hdnot-attached").click(function () {
        var awbbill = $("#input-chiho-search-awbbill").val();
        var sohd = $("#input-chiho-search-sohd").val();
        var sodentt = $("#input-chiho-search-sodenghithanhtoan").val();
        ajaxGet4 = { "get1": "SearchNotAttach", "get2": awbbill, "get3": sohd, "get4": sodentt };
        jsonData = JSON.stringify({ ajaxGet4 });
        fncSearch(jsonData);
    })
    // End tìm kiesm không có file đính kèm
    // Start Tìm kiếm chi hộ
    $("#btn-chiho-search").click(function () {
        var awbbill = $("#input-chiho-search-awbbill").val();
        var sohd = $("#input-chiho-search-sohd").val();
        var sodentt = $("#input-chiho-search-sodenghithanhtoan").val();
        ajaxGet4 = { "get1": "Search", "get2": awbbill, "get3": sohd, "get4": sodentt };
        jsonData = JSON.stringify({ ajaxGet4 });
        fncSearch(jsonData);
    })
    // End Tìm kiếm chi hộ
    // Start Tìm kiếm hóa đơn chưa được duyệt
    $("#btn-chiho-hdchuadk").click(function () {
        ajaxGet4 = { "get1": "HDChuaDK" };
        jsonData = JSON.stringify({ ajaxGet4 });
        fncSearch(jsonData);
    })
    // End Tìm kiếm hóa đơn chưa được duyệt
    $("#input-chiho-search-nguoichuyenkhoan").on("change", function () {
        var nguoichuyenkhoan = $(this).val();
        ajaxGet4 = { "get1": "NguoiChuyenKhoan", "get2": nguoichuyenkhoan, "get3": "", "get4": "" };
        jsonData = JSON.stringify({ ajaxGet4 });
        fncSearch(jsonData);
    })
    // Start Hàm tìm kiếm
    function fncSearch(jsonData){
        $.ajax({
            type: "POST",
            url: "QuanLyChiHo.aspx/ReChiHoTimKiem",
            data: jsonData,
            contentType: "application/json; charset=utf-8",
            dataType: "json",
            async: false,
            success: function (responsive) {
                d = responsive.d;
                createTableChiHo(d, true);
            },
            error: function (responsive) {
                alert("Có lỗi xảy ra! Vui lòng F5(Refresh)!");
                console.error("Lỗi xảy ra trong hàm tìm kiếm: ", responsive);
            }

        })
    }
    // End Hàm tìm kiếm
    // Start Reset tìm kiếm
    $("#btn-chiho-search-reset").click(function () {
        $(".input-chiho-search-clear").val("");
        createTableChiHo(null, false);
    })
    // End Reset tìm kiếm
    // Start Duyệt đề nghị thanh toán
    $("#btnDuyetDNTT").click(function () {
        if($(this).attr("DaDuyetDNTT") != "True"){
            var dnttId = $(this).attr("dnttId");
            if(dnttId != ""){
                swal.fire({
                    title: "Xác nhận duyệt đề nghị thanh toán",
                    text: "Bạn có chắc chắn muốn duyệt đề nghị thanh toán này không?",
                    icon: "warning",
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'Đồng ý, duyệt đề nghị thanh toán',
                    cancelButtonText: 'Hủy'
                })
                .then((result) => {
                    if (result.value) {
                        //DuyetDNTT
                        ajaxGet = { "get": dnttId };
                        jsonData = JSON.stringify({ ajaxGet });
                        $.ajax({
                            type: "POST",
                            url: "QuanLyChiHo.aspx/DuyetDNTT",
                            data: jsonData,
                            contentType: "application/json; charset=utf-8",
                            dataType: "json",
                            async: false,
                            success: function (responsive) {
                                d = responsive.d;
                                
                                fncDaDuyetDNTT(d.DaDuyet, d.NguoiDuyet, d.NgayDuyet);
                                
                            }
                        })
                    }
                })
            }
            
        }else{
            console("Đề nghị thanh toán đã được duyệt!");
        }
        
    })
    // End Duyệt đề nghị thanh toán
    // Start Hàm duyệt đề nghị thanh toán
    function fncDaDuyetDNTT(daDuyet, nguoiDuyet, ngayDuyet){
        if(daDuyet == "True"){
            $("#btnDuyetDNTT").removeClass("btn-danger").addClass("btn-success");
            $("#btnDuyetDNTT").html('<i class="fas fa-check-circle"></i> ' 
                + nguoiDuyet 
            + ' đã duyệt '
            + convertDate(ngayDuyet)[1]
            + ' '
            + convertDate(ngayDuyet)[2]
            ); 
            $(".da-duyet-an").hide();
            $("#btnDuyetDNTT").attr("DaDuyetDNTT", "True");
        }else{
            $("#btnDuyetDNTT").removeClass("btn-success").addClass("btn-danger");
            $("#btnDuyetDNTT").html('<i class="fas fa-check"></i> Duyệt DNTT');
            $(".da-duyet-an").show();
            $("#btnDuyetDNTT").attr("DaDuyetDNTT", "False");
        }
    }
    // End Hàm duyệt đề nghị thanh toán
    // Start Tải file excel
    $("#btnDownloadData").click(function () {
        var khachHang = $("#input-taidulieu-khachhang").val();
        var tungay = dmy2ymd($("#input-taidulieu-tungay").val());
        var denngay = dmy2ymd($("#input-taidulieu-denngay").val());
        //console.table({ "Khách hàng": khachHang, "Từ ngày": tungay, "Đến ngày": denngay });

        ajaxGet4 = { "get1": khachHang, "get2": tungay, "get3": denngay, "get4": ""};
        jsonData = JSON.stringify({ ajaxGet4 });
        $.ajax({
            type: "POST",
            url: "QuanLyChiHo.aspx/TaiDuLieu",
            data: jsonData,
            contentType: "application/json; charset=utf-8",
            dataType: "json",
            async: false,
            success: function (responsive) {
                d = responsive.d;
                //console.log(d);
                if (d != "error") {
                    window.open("../DownloadCacheFile.aspx?FileName=" + d, "_blank");
                } 
            }
        })  
    })
    // End Tải file excel
    // Start Tải file excel
    $("#btn-chiho-taidulieu").click(function () {
       //modal open modalTaiDuLieu
       $("#modalTaiDuLieu").modal("show");
       $("#input-taidulieu-tungay").datepicker("setDate", new Date(chDateNow.getFullYear(), chDateNow.getMonth(), 1));
       $("#input-taidulieu-denngay").datepicker("setDate", new Date(chDateNow.getFullYear(), chDateNow.getMonth(), chDateNow.getDate()));   
    })
    // End Tải file excel
    // Start Check hóa đơn
    $("#btn-chiho-checkhoadon").click(function () {
       
        $("#myModalViewCheckHoaDon").modal("show");
       
    })
    // End Check hóa đơn

    // Hàm để xử lý các yêu cầu theo từng nhóm
    function processBatch(batchSize, delay, callback) {
        let index = 0;

        function sendBatch() {
            const batch = kiemTraHoaDonData.slice(index, index + batchSize);
            if (batch.length === 0) {
                if (callback) callback(); // Gọi callback khi hoàn thành
                return;
            }

            batch.forEach(item => {
                const search5 = { 
                    "param1": item.param1, 
                    "param2": item.param2, 
                    "param3": item.param3, 
                    "param4": item.param4, 
                    "param5": item.param5
                };
                const jsonData = JSON.stringify({ search5 });
                KiemTraHoaDon(jsonData).done(() => {
                    checkedCount++;
                    updateButtonText();
                });
            });

            index += batchSize;
            setTimeout(sendBatch, delay); // Gửi nhóm tiếp theo sau một khoảng thời gian
        }

        sendBatch(); // Bắt đầu gửi nhóm đầu tiên
    }

    function updateButtonText() {
        //console.log(checkedCount);
        $("#btn-luu-CheckHoaDon").text(`Đang check hóa đơn ${checkedCount}/${totalInvoices}`);
    }

    function KiemTraHoaDon(jsonData) {
        return $.ajax({
            type: "POST",
            url: "QuanLyChiHo.aspx/KiemTraHoaDon",
            data: jsonData,
            contentType: "application/json; charset=utf-8",
            dataType: "json",
            async: true,
            success: function (responsive) {
                const d = responsive.d;
                if (d[0] == "0") {  
                        return;

                }
                //console.log(new Date().toLocaleString() + " - " + d[0]);
                $("#td-kiemtrahoadon-" + d[2] + "-" + d[3]).html(d[1]);
                $("#td-kiemtrahoadon-" + d[2] + "-" + d[3] +"-tongtientruocthue").html(fncTachPhanNghin(d[4]));
                $("#td-kiemtrahoadon-" + d[2] + "-" + d[3] +"-tongtiensauthue").html(fncTachPhanNghin(d[5]));
                if (d[0] == "1" || d[0] == "9" || d[0] == "10") {
                    $("#td-kiemtrahoadon-" + d[2] + "-" + d[3]).parent('tr').hide();
                }

                if (d[0] == "1") {
                    $("#td-kiemtrahoadon-" + d[2] + "-" + d[3]).addClass("tr-success");
                    $("#td-kiemtrahoadon-" + d[2] + "-" + d[3]).parent('tr').addClass('tr-hoa-don-da-cap-nhat');
                    successCount++;
                    $("#btn-CheckHoaDon-DaCapNhat").text(`${successCount}/${totalInvoices} HĐ Đã cập nhật`); 
                }
                if (d[0] == "7") {
                    $("#td-kiemtrahoadon-" + d[2] + "-" + d[3]).addClass("tr-warning1");
                    $("#td-kiemtrahoadon-" + d[2] + "-" + d[3]).parent('tr').addClass('tr-hoa-don-sai-thanh-tien');
                    saiThanhTienCount++;
                    
                    $("#btn-CheckHoaDon-SaiThanhTien").text(`${saiThanhTienCount}/${totalInvoices} HĐ Sai thành tiền`);
                }
                if (d[0] == "8") {
                    $("#td-kiemtrahoadon-" + d[2] + "-" + d[3]).addClass("tr-warning2");
                    $("#td-kiemtrahoadon-" + d[2] + "-" + d[3]).parent('tr').addClass('tr-hoa-don-nguoi-mua-khong-phai-alse');
                    nguoiMuaKhongPhaiALSECount++;
                    $("#btn-CheckHoaDon-NguoiMuaKhongPhaiALSE").text(`${nguoiMuaKhongPhaiALSECount}/${totalInvoices} HĐ Người mua không phải ALSE`);
                }
                if (d[0] == "9") {
                    $("#td-kiemtrahoadon-" + d[2] + "-" + d[3]).addClass("tr-warning3");
                    $("#td-kiemtrahoadon-" + d[2] + "-" + d[3]).parent('tr').addClass('tr-hoa-don-khong-ton-tai-shd-va-mst');
                    khongTonTaiSHDvaMSTCount++;
                    $("#btn-CheckHoaDon-KhongTonTaiSHDvaMST").text(`${khongTonTaiSHDvaMSTCount}/${totalInvoices} HĐ Không tồn tại`);
                }
                if (d[0] == "10") {
                    $("#td-kiemtrahoadon-" + d[2] + "-" + d[3]).addClass("tr-success");
                    $("#td-kiemtrahoadon-" + d[2] + "-" + d[3]).parent('tr').addClass('tr-hoa-don-da-check');
                    hoadonDaCheckCount++;
                    $("#btn-CheckHoaDon-DaCheckHoaDon").text(`${hoadonDaCheckCount}/${totalInvoices} HĐ Đã check`);
                }

                
            },
            error: function (responsive) {
                alert("Có lỗi xảy ra! Vui lòng F5(Refresh)!");
                console.error("Lỗi xảy ra trong hàm tìm kiếm: ", responsive);
            }
        });
    }
    $("#btn-CheckHoaDon-DaCapNhat").click(function () {
        $(".tr-check-hoa-don").hide();
        $(".tr-hoa-don-da-cap-nhat").show();
    })
    $("#btn-CheckHoaDon-SaiThanhTien").click(function () {
        $(".tr-check-hoa-don").hide();
        $(".tr-hoa-don-sai-thanh-tien").show();
    })
    $("#btn-CheckHoaDon-NguoiMuaKhongPhaiALSE").click(function () {
        $(".tr-check-hoa-don").hide();
        $(".tr-hoa-don-nguoi-mua-khong-phai-alse").show();
    })
    $("#btn-CheckHoaDon-KhongTonTaiSHDvaMST").click(function () {
        $(".tr-check-hoa-don").hide();
        $(".tr-hoa-don-khong-ton-tai-shd-va-mst").show();
    })
    $("#btn-CheckHoaDon-DaCheckHoaDon").click(function () {
        $(".tr-check-hoa-don").hide();
        $(".tr-hoa-don-da-check").show();
    })
    $("#btn-luu-CheckHoaDon").click(function () {
        const batchSize = 5; // Số lượng yêu cầu trong mỗi nhóm
        const delay = 300; // Thời gian nghỉ giữa các nhóm (ms)
        checkedCount = 0; // Bộ đếm số lượng hóa đơn đã kiểm tra
        totalInvoices = kiemTraHoaDonData.length; // Tổng số hóa đơn cần kiểm tra
        successCount = 0;
        saiThanhTienCount = 0;
        nguoiMuaKhongPhaiALSECount = 0;
        khongTonTaiSHDvaMSTCount = 0;
        hoadonDaCheckCount = 0;
        $("#btn-CheckHoaDon-DaCapNhat").text(`0 HĐ Đã cập nhật`);
        $("#btn-CheckHoaDon-SaiThanhTien").text(`0 HĐ Sai thành tiền`);
        $("#btn-CheckHoaDon-NguoiMuaKhongPhaiALSE").text(`0 HĐ Người mua không phải ALSE`);
        $("#btn-CheckHoaDon-KhongTonTaiSHDvaMST").text(`0 HĐ Không tồn tại SHĐ và MST`);
        $("#btn-CheckHoaDon-DaCheckHoaDon").text(`0 HĐ Đã check`);
        // Vô hiệu hóa nút để ngăn chặn việc click nhiều lần
        $(this).prop('disabled', true);

        processBatch(batchSize, delay, () => {
            // Kích hoạt lại nút sau khi xử lý xong
            $("#btn-luu-CheckHoaDon").prop('disabled', false);
            $("#btn-luu-CheckHoaDon").text(`Đã check xong ${totalInvoices}/${totalInvoices}`);
        });
    });

    $("#btnTaiHDDNTT").click(function () {
        var dnttId = $(this).attr("dnttId");
        var jsonData = JSON.stringify({ dnttId: dnttId });
            $.ajax({
                type: "POST",
                url: "QuanLyChiHo.aspx/TaiHoaDonByDNTTId",
                data: jsonData,
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                async: false,
                success: function (responsive) {                    
                    
                    var taiHoaDonInfo = responsive.d;
                    var zipFileName = taiHoaDonInfo.ZipFileName;
                    
                    if (zipFileName != "error") {
                        window.open("../DownloadCacheFile.aspx?FileName=" + zipFileName, "_blank");
                    } 

                },
                error: function (jqXHR, textStatus, errorThrown) {
                    console.error("Có lỗi xảy ra trong quá trình tải hóa đơn: " + textStatus + " - " + errorThrown);
                    }
                })
    })
}

// Start Hàm xử lý sự kiện change
function fncChange() {
    // Start Xử lý sự kiện change thanh tiền
    $("#input-chiho-thanhtien").change(function () {
        var parseSoTienSauVAT = parseInt($(this).val().replace(/,/g, ""));
        var soTienTruocVAT = tinhSoTienTruocVAT(parseSoTienSauVAT);
        $("#input-chiho-sotienthue").val(fncTachPhanNghin(soTienTruocVAT));
    });
    // End Xử lý sự kiện change thanh tiền
    // Start Xử lý sự kiện change khách hàng

    $("#myModalViewChiHo").on("change", "#input-chiho-khachhang", function () {
        // var _loaihinh = $("#select-chiho-loaihinh").val();
        // if (_loaihinh == "") {
        //     alert("Vui lòng chọn loại hình xong tiếp tục chọn khách hàng!");
        // } else {
        //     // load mawb hawb
        //     reMawbHawb($(this).val());
        //     loadAWB(_loaihinh);
        // }
        // Tạo dropdown list Người mua
       var html_option_nguoimua = "<option value=\"ALSE\">ALSE</option>";
       html_option_nguoimua += "<option value=\""+$(this).val()+"\">"+$(this).val()+"</option>";
       html_option_nguoimua += "<option value=\"OTHER\">OTHER</option>";
        $("#input-chiho-nguoimua").empty().append(html_option_nguoimua);
        var khachHangSelected = $(this).val();
        if(khachHangSelected != ""){
            getBillAWB();
            $("#input-chiho-awbbill").prop('disabled', false);
        }else{
            $("#input-chiho-awbbill").prop('disabled', true);
        }
       
    })
    // End Xử lý sự kiện change khách hàng
    // Start Xử lý sự kiện change loại hình
    $("#myModalViewChiHo").on("change", "#select-chiho-loaihinh", function () {
        //reMawbHawb($("#select-chiho-khachhang").val());
        //var cb_value = $(this).val();
        //loadAWB(cb_value);
        var loaiHinhSelected = $(this).val();
        if(loaiHinhSelected != ""){
            loadKH(loaiHinhSelected);
            $("#input-chiho-khachhang").prop('disabled', false);
        }else{
            $("#input-chiho-khachhang").prop('disabled', true);
            $("#input-chiho-awbbill").prop('disabled', true);
        }
        
    })
    // End Xử lý sự kiện change loại hình
    // Start Xử lý sự kiện change ncc
    $("#myModalViewChiHo").on("change", "#input-chiho-ncu", function () {
        var nccSelected = $(this).val();
        if (nccSelected === "") {
            $("#input-chiho-kihieuhd").val("");
            $("#input-chiho-tennguoiban").val("");
        } else {
            
            var tempListNCC = listNCC.filter(function(ncc){
                return ncc.NCC == nccSelected;
            });
            if (tempListNCC.length > 0) {
                $("#input-chiho-kihieuhd").val(tempListNCC[0].KiHieuHoaDon1);
                $("#input-chiho-tennguoiban").val(tempListNCC[0].TenCty);
            }
            
        }
    });
    // End Xử lý sự kiện change ncc
    // Start Xử lý sự kiện change file
    $('#input-chiho-uploadfile').on('change', function() {
        $('#input-chiho-tentep').val($('#input-chiho-awbbill').val() + '-' + $('#input-chiho-ncu').val() + '-' + $('#input-chiho-khachhang').val() + '-' + $('#input-chiho-sodenghithanhtoan').val());
    });
    // End Xử lý sự kiện change file
    // Start Xử lý sự kiện change AWB
    $('#multiTextBoxAWB').on('input', function () {
        var lines = $(this).val().split('\n').filter(line => line.trim() !== '').length;
        $('#btnDownloadHoaDon').text('Download ' + lines + ' AWB/BILL');
    });
    // End Xử lý sự kiện change AWB
    // Start Xử lý sự kiện change loại hình đề nghị thanh toán
    $("#modalTaoDNTT").on("change", "#select-dntt-loaihinh", function () {
        var loaiHinhSelected = $(this).val();
        if(loaiHinhSelected != ""){
            loadKH(loaiHinhSelected);
            $("#input-dntt-khachhang").prop('disabled', false);
        }else{
            $("#input-dntt-khachhang").prop('disabled', true);
        }
        
    })
    // End Xử lý sự kiện change loại hình đề nghị thanh toán
    // Start Xử lý sự kiện change khách hàng đề nghị thanh toán
    $("#modalTaoDNTT").on("change", "#input-dntt-khachhang, #select-dntt-nguoichuyenkhoan, #select-dntt-loaihinh", function () {
        var khachHangSelected = $("#input-dntt-khachhang").val();
        var nguoiChuyenKhoanSelected = $("#select-dntt-nguoichuyenkhoan").val();
        var loaiHinhSelected = $("#select-dntt-loaihinh").val();
        if(khachHangSelected != ""){
            loadDNTTChiHo();
        }else{
            $("#tbl-dntt-chiho tbody").empty();
        }
        
    })
    // End Xử lý sự kiện change khách hàng đề nghị thanh toán
    // Start Xử lý sự kiện show modal đề nghị thanh toán
    $("#modalTaoDNTT").on("shown.bs.modal", function () {
        loadDNTTChiHo();
        //$("#input-dntt-nguoidenghi").val($("#username").text().trim());
    })
    // End Xử lý sự kiện show modal đề nghị thanh toán
    // Start Xử lý sự kiện hide modal đề nghị thanh toán
    $("#modalTaoDNTT").on('hidden.bs.modal', function () {
        $("#tbl-dntt-chiho tbody").empty();
        $("#input-dntt-khachhang").val("ALL");
        $("#select-dntt-loaihinh").val("ALL");
        $("#input-dntt-nguoidenghi").val("");
        $("#input-dntt-ngaytao").val("");
        $("#input-dntt-noidungthanhtoan").val("");
        $("#input-dntt-id").val("");
        $("#tbl-dntt-chitiet tbody").empty();
        $("#btnInDNTT").hide();
        $("#btnTaiDNTTExcel").hide();
        themChiHoId = [];
        xoaChiHoId = [];
    })
    // End Xử lý sự kiện hide modal đề nghị thanh toán
    $("#myModalViewCheckHoaDon").on("hidden.bs.modal", function () {
        $("#tbl-upload-check-hoa-don tbody").empty();
        $("#btn-luu-CheckHoaDon").text("Check Hoá Đơn");
    })
    // Start Xử lý sự kiện click
    $("#checkboxSuccess-dathanhtoanncc").click(function(){
        if($(this).is(":checked")){
            $("#input-chiho-ngaythanhtoanncc").prop("disabled", false);
            $("#input-chiho-ngaythanhtoanncc").datepicker("setDate", new Date(chDateNow.getFullYear(), chDateNow.getMonth(), chDateNow.getDate()));
            $('#checkboxSuccess-dathanhtoanncc').parent('label').contents().filter(function() {
                return this.nodeType === 3; // Chọn các node văn bản
            }).last().replaceWith($("#username").text().trim() +' đã thanh toán NCC');
        }else{
            $("#input-chiho-ngaythanhtoanncc").prop("disabled", true);
            $("#input-chiho-ngaythanhtoanncc").val("");
            $('#checkboxSuccess-dathanhtoanncc').parent('label').contents().filter(function() {
                return this.nodeType === 3; // Chọn các node văn bản
            }).last().replaceWith('Đã thanh toán NCC');
        }
    })
    // End Xử lý sự kiện click
    // Start change chọn file check hoá đơn
    $("#f_UploadCheckHoaDon").on("change", function (e) {
        kiemTraHoaDonData = [];
        $("#btn-luu-CheckHoaDon").text("Đang đọc file...");
        var file = e.target.files[0];
        if(file.type == "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" || file.type == "application/vnd.ms-excel"){
            if (!file) {
                return;
            }
        
            const reader = new FileReader();
            reader.onload = function(e) {
                const data = new Uint8Array(e.target.result);
                const workbook = XLSX.read(data, { type: 'array' });
        
                // Giả sử bạn muốn đọc dữ liệu từ sheet đầu tiên
                const firstSheetName = workbook.SheetNames[0];
                const worksheet = workbook.Sheets[firstSheetName];
        
                // Chuyển đổi dữ liệu sheet thành JSON
                const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
        
                // Hiển thị dữ liệu JSON trong console
                //console.log(jsonData);
                // Xóa nội dung cũ trong tbody
                $("#tbl-upload-check-hoa-don tbody").empty();
                let _tongsohoadon = 0;
                _tongsohoadon = jsonData.length - 4;
                $("#btn-luu-CheckHoaDon").text("Click để check " + _tongsohoadon + " Hoá Đơn");
                // Bắt đầu từ item thứ 4
                for (let i = 4; i < jsonData.length; i++) {
                    let row = jsonData[i];
                    let htmlRow = "<tr class=\"tr-check-hoa-don\">";

                    chd_kyhieuhoadon = row[2];
                    chd_sohd = row[3];
                    chd_ngayhd = row[4];
                    chd_masothue = row[5];
                    chd_tongtientruocthue = row[8];
                    chd_tongtiensauthue = row[12];

                    htmlRow += `<td>${row[0]}</td>`;
                    htmlRow += `<td>${chd_kyhieuhoadon}</td>`;
                    htmlRow += `<td>${chd_sohd}</td>`;
                    htmlRow += `<td>${chd_ngayhd}</td>`;
                    htmlRow += `<td>${chd_masothue}</td>`;
                    htmlRow += `<td>${row[6]}</td>`;
                   
                    kiemTraHoaDonData.push({
                        param1: chd_sohd,
                        param2: chd_masothue,
                        param3: chd_tongtiensauthue,
                        param4: chd_kyhieuhoadon,
                        param5: dmy2ymd(chd_ngayhd)
                    });
                    htmlRow += "<td id=\"td-kiemtrahoadon-" + chd_sohd + "-" + chd_masothue + "-tongtientruocthue\"></td>";
                    htmlRow += "<td id=\"td-kiemtrahoadon-" + chd_sohd + "-" + chd_masothue + "-tongtiensauthue\"></td>";
                    htmlRow += `<td>${fncTachPhanNghin(chd_tongtientruocthue)}</td>`;
                    htmlRow += `<td>${fncTachPhanNghin(chd_tongtiensauthue)}</td>`;
                    htmlRow += "<td id=\"td-kiemtrahoadon-" + chd_sohd + "-" + chd_masothue + "\"></td>";
                    htmlRow += "</tr>";
                    $("#tbl-upload-check-hoa-don tbody").append(htmlRow);
                }
                //console.log(kiemTraHoaDonData);
            };
        
            reader.readAsArrayBuffer(file);
        }else{
           alert("Vui lòng chọn file excel!");
        }

    })
    // End change chọn file check hoá đơn
    
}
// End Hàm xử lý sự kiện change
// Start Hàm xử lý sự kiện show hide modal
function fncModal() {
    // Start Xử lý sự kiện show modal tải file excel
    $('#modalQuanLyChiHoExcel').on('shown.bs.modal', function () {
        $(document).off('focusin.bs.modal');
        $(window).trigger("resize"); // bug modal > show excel
    });
    // End  Xử lý sự kiện show modal tải file excel
    // Start Xử lý sự kiện hide modal chi hộ
    $('#myModalViewChiHo').on('hide.bs.modal', function () {
        $("#input-chiho-ncu").val("");
        $("#select-chiho-loaihinh").val("");
        $("#input-chiho-ngaychuyenkhoan").val("");
        $("#input-chiho-khachhang").val("");
        $("#input-chiho-awbbill").val("");
        $("#input-chiho-check").val("");
        $("#input-chiho-kihieuhd").val("");
        $("#input-chiho-sohd").val("");
        $("#input-chiho-ngayhd").val("");
        $("#input-chiho-tennguoiban").val("");
        $("#input-chiho-sotienthue").val("");
        $("#input-chiho-thanhtien").val("");
        $("#input-chiho-tt-dck").val("");
        $("#input-chiho-tt-dntt").val("");
        $("#input-chiho-tt-dck").val("");
        $("#input-chiho-idnhap").val("");
        $("#input-chiho-ghichu").val("");
        $("#input-chiho-sodenghithanhtoan").val("");
        arrTempData = {};
        arrUploadData = {};
        $("#tbl-upload-imgzone tbody tr").remove();
        var html_option_nguoimua = "<option value=\"ALSE\">ALSE</option>";
        html_option_nguoimua += "<option value=\"OTHER\">OTHER</option>";
        $("#input-chiho-nguoimua").empty().append(html_option_nguoimua);
        $("#tbl-upload-imgzone tbody").empty();
        $("#input-chiho-ngaythanhtoanncc").val("");
        $("#input-chiho-ngaythanhtoanncc").prop("disabled", true);
    }); 
    // End Xử lý sự kiện hide modal chi hộ
    // Start Xử lý sự kiện show modal tải hóa đơn
    $('#modalTaiHoaDon').on('shown.bs.modal', function () {
        $('#multiTextBoxAWB').focus();
    });
    // End Xử lý sự kiện show modal tải hóa đơn
    // Start Xử lý sự kiện hide modal tải hóa đơn
    $('#modalTaiHoaDon').on('hidden.bs.modal', function () {
        $('#multiTextBoxAWB').val('');
        $('#tbl-taihoadon tbody').empty();
        $('#tbl-taihoadon').hide();
        
        $('#btnDownloadHoaDon').text('Download');
    });
    // End Xử lý sự kiện hide modal tải hóa đơn
}
// End Hàm xử lý sự kiện show hide modal
// Start Hàm load dữ liệu
function loadMainView(){
    usernameId = parseInt($("#username").attr("userid"));
    var nguoichuyenkhoanmacdinh = [186, 195, 209];
    if(nguoichuyenkhoanmacdinh.includes(usernameId)){
        $("#input-chiho-search-nguoichuyenkhoan").val(usernameId);
    }else{
        $("#input-chiho-search-nguoichuyenkhoan").val("0");
    }
    var nguoichuyenkhoanmacdinh2 = [186, 195, 209, 20];
    if(nguoichuyenkhoanmacdinh2.includes(usernameId)){
        $("#select-dntt-nguoichuyenkhoan").val(usernameId);
    }else{
        $("#select-dntt-nguoichuyenkhoan").val("0");
    }

    ajaxGet = { "get": "" };  // note get top 10
    jsonData = JSON.stringify({ ajaxGet });

    $.ajax({
        type: "POST",
        url: "QuanLyChiHo.aspx/ReChiHo",
        data: jsonData,
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        async: false,
        success: function (responsive) {
            d = responsive.d;
            createTableChiHo(d, false);
        },
        error: function (responsive) {
            alert("Có lỗi xảy ra! Vui lòng F5(Refresh)!");
            console.error("Lỗi xảy ra trong hàm loadMainView: ", responsive);
        }

    });
}
// End Hàm load dữ liệu
// Start Hàm tạo bảng chi hộ
function createTableChiHo(responData, isSearch = false){
    if (responData == null) {
        chihos          = [];
        hdChuaDK        = 0;
        HdKhongNCC      = 0;
        HdKhongSoHD     = 0;
        Page            = 1;
        PageSize        = 50;
        TotalRecord     = 50;
    } else {
        chihos = responData.chiHos;
        hdChuaDK = responData.HdChuaDK;
        HdKhongNCC = responData.HdKhongNCC;
        HdKhongSoHD = responData.HdKhongSoHD;
        Page = responData.Page;
        PageSize = responData.PageSize;
        TotalRecord = responData.TotalRecord;
    }
    html_body = "";
    if(chihos != null && chihos.length > 0){    
        $.each(chihos, function (key, val) {
            html_body += "<tr class=\""+(isSearch ? "tr-search" : "tr-main")+"\">";
            html_body += "<td class=\"\">" + 
                        (val.Check_ChiHo === "True" 
                            ? "<i class=\"fa fa-check\" style=\"color: green;\"></i>" 
                            : "") + 
                        "</td>";
            html_body += "<td class=\"td-awb\">" + val.NCU.toUpperCase() + "</td>";
            html_body += "<td>" + val.LoaiHinh + "</td>";
            html_body += "<td>" + val.KhachHang.toUpperCase() + "</td>";
            html_body += "<td>" + val.AWBBILL + "</td>";
            html_body += "<td>" + val.KiHieuHD + "</td>";
            html_body += "<td "+(val.ThanhToanNCC == "True" ? "style=\"background-color:#428bcabf\"" : "") + ">" + val.SoHD +"</td>";
            html_body += "<td>" + val.TenNguoiBan + "</td>";
            html_body += "<td>" + val.HoaDonKhach + "</td>";
            html_body += "<td>" + val.PhiChungTuNhap + "</td>";
            html_body += "<td>" + fncTachPhanNghin(val.ThanhTien)  + "</td>";
            html_body += "<td>" + fncTachPhanNghin(val.SoTruocThue)  + "</td>";
            html_body += "<td>" + convertDate(val.NgayThanhToanNCC)[1] + "</td>";
            html_body += "<td "+ (val.TrangThaiDNTT == "True" ? "style=\"background-color:#428bcabf\"" : "") + "class=\"td-view-dntt\" dnttId=\"" + val.SoDeNghiThanhToan + "\">" + val.SoDeNghiThanhToan + "</td>";  
            html_body += "<td>" + val.GhiChu + "</td>";
            html_body += "<td>" + convertDate(val.NgayTao)[1] + "</td>";
            html_body += "<td class=\"td-chucnang\">" + "<a class=\"label label-"+(val.FileDinhKem == 0 ? "default" : "info")+" btn-chiho-dinhkem\" attrID=\"" + val.Id + "\">Đính kèm</a>";
            html_body +=  "<a class=\"label label-success btn-chiho-qr\" ThanhToanNCC=\""+ val.ThanhToanNCC +"\" Khachhang=\""+ val.KhachHang +"\" NCC=\""+ val.NCU.toUpperCase() + "\" TenPhi=\""+ val.PhiChungTuNhap +"\" AWBBILL=\""+ val.AWBBILL +"\" attrID=\"" + val.Id + "\" attrThanhToan=\"" + val.ThanhTien + "\" attrKiHieuHD=\"" + val.KiHieuHD + "\">Xem QR</a>";
            html_body += "</br>";
            html_body += "<a class=\"label label-warning btn-chiho-sua\" AWBBILL=\""+ val.AWBBILL +"\" attrID=\"" + val.Id + "\">Sửa</a>";
            html_body += "<a class=\"label label-danger btn-chiho-xoa\" AWBBILL=\""+ val.AWBBILL +"\" sodenghithanhtoan=\"" + val.SoDeNghiThanhToan + "\" attrID=\"" + val.Id + "\">Xóa</a>" + "</td>";
            html_body += "</tr>";
        });
    }
    if(isSearch){// nếu có tìm kiếm, lúc này table đã có data
        $("#tbl-chiho tbody tr.tr-main").hide();
        $("#tbl-chiho tbody tr.tr-search").remove();
        $("#tbl-chiho tbody").append(html_body);

        // Cập nhật pagination cho kết quả tìm kiếm
        var searchRows = $("#tbl-chiho tbody tr.tr-search").toArray();
        setPaginationData(searchRows);
    }else{// nếu không tìm kiếm
        // trường hợp đã có data thì chỉ cần làm như bên dưới
        $("#tbl-chiho tbody tr.tr-main").show();
        $("#tbl-chiho tbody tr.tr-search").remove();
        // trường hợp không có data thì thêm vào table
        if(html_body != ""){
            $("#btn-chiho-hdchuadk").text(hdChuaDK + " HĐ Chưa ĐK");
            $("#btn-chiho-notncc").text(HdKhongNCC + " HĐ Không NCC");
            $("#btn-chiho-notsohd").text(HdKhongSoHD + " HĐ Không Số HĐ");
            $("#tbl-chiho tbody").empty().append(html_body);

            // Cập nhật pagination cho dữ liệu chính
            var mainRows = $("#tbl-chiho tbody tr.tr-main").toArray();
            setPaginationData(mainRows);
        } else {
            // Reset pagination khi không có dữ liệu
            resetPagination();
        }
    }
}
// End Hàm tạo bảng chi hộ
// Start Hàm load đề nghị thanh toán chi hộ
function loadDNTTChiHo(){
    ajaxGet3 = { "get1": $("#select-dntt-loaihinh").val(), "get2": $("#select-dntt-nguoichuyenkhoan").val(), "get3": $("#input-dntt-khachhang").val() };  // note get top 10
    jsonData = JSON.stringify({ ajaxGet3 });

    $.ajax({
        type: "POST",
        url: "QuanLyChiHo.aspx/ReDNTTChiHo",
        data: jsonData,
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        async: false,
        success: function (responsive) {
            d = responsive.d;
            html_body = "";
            //console.log(d)
            let tempId = "";
            let tempSoHoaDon = "";
            let tempKhachHang = [];
            let tempAwbBill = [];
            let tempTenPhi = []; // Phí chứng từ nhập
            let tempSoTienSauVat = 0; // Thành tiền
            $("#tbl-dntt-chiho tbody").empty();

            $.each(d, function (key, val) {
                if(tempSoHoaDon != val.SoHD || val.SoHD == ""){
                    tempId = val.Id;
                    tempSoHoaDon = val.SoHD;
                    tempKhachHang = [];
                    tempAwbBill = [];
                    tempTenPhi = [];
                    tempKhachHang.push(val.KhachHang);
                    tempAwbBill.push(val.AWBBILL);
                    tempTenPhi.push(val.PhiChungTuNhap);
                    tempSoTienSauVat = Number(val.ThanhTien);
                    html_body = "<tr>";
                    html_body += "<td class=\"\">" +
                        // "<input class=\"td-checkbox td-cb-dntt-child td-cb-dntt-child-" + val.SoHD + "\" " +
                        // "id=\"td-cb-dntt-" + val.SoHD + "\" " +
                        // "type=\"checkbox\" " +
                        // "value=\"" + val.Id + "\" " +
                        // "tr-attr-id=\"" + val.Id + "\" " +
                        // "tr-attr-sohoadon=\"" + val.SoHD + "\" " +
                        // "tr-attr-khachhang=\"" + val.KhachHang + "\" " +
                        // "tr-attr-kihieu=\"" + val.KiHieuHD + "\" " +
                        // "tr-attr-awbbill=\"" + val.AWBBILL + "\"" +
                        // "tr-attr-ngayhd=\"" + convertDate(val.NgayHD)[1] + "\"" +
                        // "tr-attr-tennguoiban=\"" + val.TenNguoiBan + "\"" +
                        // "tr-attr-phichungtunhap=\"" + val.PhiChungTuNhap + "\"" +
                        // "tr-attr-thanhtien=\"" + val.ThanhTien + "\"/>" +
                        "</td>";
                    html_body += "<td id=\"td-dntt-sohoadon-" + val.SoHD + "\" "+(val.ThanhToanNCC == "True" ? "style=\"background-color:#428bcabf\"" : "") + ">" + val.SoHD +"</td>";
                    html_body += "<td id=\"td-dntt-khachhang-" + val.SoHD + "\">" + val.KhachHang + "</td>";
                    html_body += "<td id=\"td-dntt-awbbill-" + val.SoHD + "\">" + val.AWBBILL + "</td>";
                    html_body += "<td>" + val.KiHieuHD + "</td>";
                    html_body += "<td id=\"td-dntt-tennguoiban-" + val.SoHD + "\">" + val.TenNguoiBan + "</td>";
                    html_body += "<td id=\"td-dntt-phichungtunhap-" + val.SoHD + "\">" + val.PhiChungTuNhap + "</td>";
                    html_body += "<td id=\"td-dntt-thanhtien-" + val.SoHD + "\">" + fncTachPhanNghin(val.ThanhTien)  + "</td>";
                    html_body += "</tr>";
                    $("#tbl-dntt-chiho tbody").append(html_body);
                }else{
                    tempId += "," + val.Id;
                    $(".td-cb-dntt-child-" + val.SoHD).attr("tr-attr-id", tempId);
                    if(!tempKhachHang.includes(val.KhachHang)){
                        tempKhachHang.push(val.KhachHang);
                        $("#td-dntt-khachhang-" + val.SoHD).text(tempKhachHang.join("-"));
                        $(".td-cb-dntt-child-" + val.SoHD).attr("tr-attr-khachhang", tempKhachHang.join("-"));
                    }
                    if(!tempAwbBill.includes(val.AWBBILL)){
                        tempAwbBill.push(val.AWBBILL);
                        $("#td-dntt-awbbill-" + val.SoHD).text(tempAwbBill.join("\n"));
                        $(".td-cb-dntt-child-" + val.SoHD).attr("tr-attr-awbbill", tempAwbBill.join("\n"));
                    }
                    if(!tempTenPhi.includes(val.PhiChungTuNhap)){
                        tempTenPhi.push(val.PhiChungTuNhap);
                        $("#td-dntt-phichungtunhap-" + val.SoHD).text(tempTenPhi.join("\n"));
                        $(".td-cb-dntt-child-" + val.SoHD).attr("tr-attr-phichungtunhap", tempTenPhi.join("\n"));
                    }
                    tempSoTienSauVat += Number(val.ThanhTien);
                    $("#td-dntt-thanhtien-" + val.SoHD).text(fncTachPhanNghin(tempSoTienSauVat));
                    $(".td-cb-dntt-child-" + val.SoHD).attr("tr-attr-thanhtien", tempSoTienSauVat);
                }
                
                
            });

            
        },
        error: function (responsive) {
            alert("Có lỗi xảy ra! Vui lòng F5(Refresh)!");
            console.error("Lỗi xảy ra trong hàm loadMainView: ", responsive);
        }

    });
}
// End Hàm load đề nghị thanh toán chi hộ
// Start Hàm load ncc
function loadNCC() {
    arrayNCC = [];
    ajaxGet = { "get": "" };
    jsonData = JSON.stringify({ ajaxGet });

    $.ajax({
        type: "POST",
        url: "QuanLyChiHoNCC.aspx/reNCC",
        data: jsonData,
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        async: false,
        success: function (responsive) {
            listNCC  = responsive.d;
            html_option = "<option value=\"\"></option>"
            $.each(listNCC, function (key, val) {
                html_option += "<option value=\"" + val.NCC.toUpperCase() + "\">" + val.TenCty.toUpperCase()+ "</option>"

                arrayNCC.push(val.NCC.toUpperCase());
            });
            $("#sltNCC").empty().append(html_option);
        },
        error: function (responsive) {
            alert("Có lỗi xảy ra! Vui lòng F5(Refresh)!");
            console.error("Lỗi xảy ra trong hàm loadNCC: ", responsive);
        }

    });
}
// End Hàm load ncc
// Start Hàm load khách hàng
function loadKH(loaiHinh) {
    if(loaiHinh == "" || loaiHinh == null){
        ajaxGet = { "get": "" };
        jsonData = JSON.stringify({ ajaxGet });
    
        $.ajax({
            type: "POST",
            url: "QuanLyChiHo.aspx/reKhachHang",
            data: jsonData,
            contentType: "application/json; charset=utf-8",
            dataType: "json",
            async: false,
            success: function (responsive) {
                listKhachHang = responsive.d; 
                //console.log(listKhachHang);
                html_option = "<option value=\"ALL\">ALL</option>";
                $.each(listKhachHang, function (key, val) {
                    html_option += "<option value=\"" + val.KhachHang + "\">" + val.KhachHang + "</option>";
                });
                $("#sltKhachHang").empty().append(html_option);
                $("#sltKhachHangDNTT").empty().append(html_option);
                $("#slt-taidulieu-khachhang").empty().append(html_option);
                $("#input-dntt-khachhang").val("ALL");
            },
            error: function (responsive) {
                alert("Có lỗi xảy ra! Vui lòng F5(Refresh)!");
            }
    
        });
    }else{
        var tempListKhachHang = listKhachHang.filter(function(khachHang){
            return khachHang.LoaiHinh == loaiHinh;
        });
        html_option = "<option value=\"ALL\">ALL</option>";
        $.each(tempListKhachHang, function (key, val) {
            html_option += "<option value=\"" + val.KhachHang + "\">" + val.KhachHang + "</option>"
        });
        $("#sltKhachHang").empty().append(html_option);
        $("#sltKhachHangDNTT").empty().append(html_option);
        $("#input-dntt-khachhang").val("ALL");
    }

    
}
// End Hàm load khách hàng
// Start Hàm load AWB
function getBillAWB() {
    ajaxGet2 = { "get1": $("#select-chiho-loaihinh").val(), "get2": $("#input-chiho-khachhang").val() };
    jsonData = JSON.stringify({ ajaxGet2 });

    $.ajax({
        type: "POST",
        url: "QuanLyChiHo.aspx/getBillAWB",
        data: jsonData,
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        async: false,
        success: function (responsive) {
            d = responsive.d;
            //console.log(d);
            //mawbhawb = d;
            html_option = "<option value=\"\"></option>"
            $.each(d, function (key, val) {
                html_option += "<option value=\"" + val + "\">" + val + "</option>"
            });
            $("#sltawb").empty().append(html_option);
        },
        error: function (responsive) {
            alert("Có lỗi xảy ra! Vui lòng F5(Refresh)!");
        }

    });
}
// End Hàm load AWB
// Start Hàm load origin
function fncLoadOrigin(input) {
    // BEGIN AJAX LOAD
    //TODO 1.
    //TODO 2.
    //TODO 3.
    ajaxGet = { "get": input };
    jsonData = JSON.stringify({ ajaxGet });
    $.ajax({
        type: "POST",
        url: "QuanLyChiHoNCC.aspx/reNCCByNCC",
        data: jsonData,
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        async: false,
        success: function (responsive) {
            d = responsive.d;
            $("#input-chiho-kihieuhd").val(d.KiHieuHoaDon1);
            $("#input-chiho-tennguoiban").val(d.TenCty);
        },
        error: function (request, status, error) {
            console.log(request.responseText);
        }
    }).done(function () {
    });
    /// END AJAX LOAD
}
// End Hàm load origin
// Start Hàm thêm mới chi hộ
function InsertUpdateChiHo(Id) {
    chiHo = {
        "Id": Id,
        "NCU": $("#input-chiho-ncu").val(),
        "LoaiHinh": $("#select-chiho-loaihinh").val(),
        "NgayCK": dmy2ymd($("#input-chiho-ngaychuyenkhoan").val()),
        "KhachHang": $("#input-chiho-khachhang").val(),
        "AWBBILL": $("#input-chiho-awbbill").val(),
        "Check_ChiHo": $("#input-chiho-check").val(),
        "KiHieuHD": $("#input-chiho-kihieuhd").val(),
        "SoHD": $("#input-chiho-sohd").val(),
        "NgayHD": dmy2ymd($("#input-chiho-ngayhd").val()),
        "TenNguoiBan": $("#input-chiho-tennguoiban").val(),
        "PhiChungTuNhap": $("#input-chiho-phichungtunhap").val(),
        "SoTruocThue": $("#input-chiho-sotienthue").val().replace(/,/g, ""),
        "ThanhTien": $("#input-chiho-thanhtien").val().replace(/,/g, ""),
        "HoaDonKhach": $("#input-chiho-nguoimua").val(),
        "TrangThaiDNTT": $("#input-chiho-tt-dntt").val(),
        "TrangThaiDoiChieuKhach": $("#input-chiho-tt-dck").val(),
        "IdNhap": $("#input-chiho-idnhap").val(),
        "GhiChu": $("#input-chiho-ghichu").val(),
        "SoDeNghiThanhToan": $("#input-chiho-sodenghithanhtoan").val(),
        "ThanhToanNCC": $("#checkboxSuccess-dathanhtoanncc").is(":checked") ? 1 : 0,
        "NgayThanhToanNCC": dmy2ymd($("#input-chiho-ngaythanhtoanncc").val()),
        "NguonDuLieu": "FORM"
    }
    console.log(chiHo);
    var messageTitle = "Thêm mới chi hộ thành công!";
    if (Id != "") {
        messageTitle = "Cập nhật chi hộ thành công!";
    }
    var fileChuaUpload = $("#tbl-upload-imgzone tbody tr.tr-upload-chuaupload").length;
    var fileDaUpload = $("#tbl-upload-imgzone tbody tr.tr-upload-daupload").length;
    //console.log(chiHo)

    $.ajax({
        type: "POST",
        url: "QuanLyChiHo.aspx/InsertUpdateChiHo",
        data: JSON.stringify({ chiHo }),
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        async: false,
        success: function (responsive) {
            d = responsive.d;
            //console.log(d)
            if (d && d != "-1") {
                if(fileChuaUpload > 0){
                    var totalFile = fileChuaUpload + fileDaUpload;
                    // trả lại data là ID của chiho
                //console.log($("#tbl-upload-imgzone tbody tr.tr-upload-chuaupload").length);
                
                    $("#div-wait").show();
                    for (var t = 0; t < 10; t++) {
                        $("#div-upload-process-bar").attr("style", "width:" + t + "%");
                        $("#div-upload-process-bar").text(t + "%");
                    }
                    fileData = new FormData();
                    //console.log(arrUploadData);
                    for (var val in arrUploadData) {
                        //console.log(arrUploadData[val]);
                        fileData.append("file", arrUploadData[val]);
                    }
                    fileData.append("folder", d);
                    fileData.append("root", "ChiHo");
                    for (var t = 10; t < 30; t++) {
                        $("#div-upload-process-bar").attr("style", "width:" + t + "%");
                        $("#div-upload-process-bar").text(t + "%");
                    }
                    if (fileData.getAll("file").length === 0) {
                        alert("Không có file nào để upload!");
                        return;
                    }else{
                        $.ajax({
                            type: "POST",
                            url: "AjaxFileUploader.ashx",
                            data: fileData,
                            contentType: false,
                            processData: false,
                            async: false,
                            success: function (responsive) {
                                for (var t = 30; t <= 100; t++) {
                                    $("#div-upload-process-bar").attr("style", "width:" + t + "%");
                                    $("#div-upload-process-bar").text(t + "%");
                                    if (t == 100) {
                                        setTimeout(function () {
                                            $("#div-upload-process-bar").text("HOÀN THÀNH");
                                        }, 1000);
                                    }
                                }
                                $("#tbl-upload-imgzone tbody tr.tr-upload-chuaupload .span-upload-trangthai").addClass("label-success")
                                    .removeClass("label-default")
                                    .text("Đã Upload");
                                $("#tbl-upload-imgzone tbody tr.tr-upload-chuaupload").addClass("tr-upload-daupload")
                                    .removeClass("tr-upload-chuaupload");
            
                                arrTempData = {};
                                arrUploadData = {};
                            },
                            error: function (responsive) {
                                alert("Có lỗi xảy ra! Vui lòng F5(Refresh)!");
                            }
                        }).done(function () {
                            $("#div-wait").hide();
                            var ajaxGet2 = { "get1": d, "get2": totalFile };
                            var jsonData = JSON.stringify({ ajaxGet2 });
                            // cập nhật file đính kèm
                            $.ajax({
                                type: "POST",
                                url: "QuanLyChiHo.aspx/UpdateFileDinhKem",
                                data: jsonData,
                                contentType: "application/json; charset=utf-8",
                                dataType: "json",
                                async: false,
                                success: function (responsive) {
                                    if (responsive.d === "ok") {
                                        console.log("Cập nhật file đính kèm thành công");
                                    } else {
                                        console.log("Cập nhật file đính kèm thất bại");
                                    }
                                },
                                error: function () {
                                    console.log("Có lỗi xảy ra trong quá trình cập nhật file đính kèm");
                                }
                            }); 
                            
                        })
                    }

                }
                loadMainView();
                swal.fire({
                    title: messageTitle,
                    //text: "Hệ thống sẽ tự tải lại sau 2s",
                    type: 'success',
                    confirmButtonText: 'OK'
                }).then(function(){
                    fncResetProcessBar();
                    if(Id == ""){
                        $("#btn-luu-chiho").hide();
                    }
                    //$("#myModalViewChiHo").modal("hide");
                    
                });
                
            }else{
                swal.fire({
                    title: "Thêm mới chi hộ thất bại",
                    text: "Báo lại cho admin để xử lý",
                    type: 'error',
                    confirmButtonText: 'OK'
                });
            }



        },
        error: function (responsive) {
            alert("Có lỗi xảy ra! Vui lòng F5(Refresh)!");
        }
    })
}
// End Hàm thêm mới chi hộ
// Start Hàm thêm mới đề nghị thanh toán
function InsertUpdateDNTT(Id) {

    if(themChiHoId.length == 0 && xoaChiHoId.length == 0){
        alert("Vui lòng chọn chi hộ để thêm vào đề nghị thanh toán!");
        return;
    }
    dntt = {
        "Id": Id,
        "KhachHang": $("#input-chiho-khachhang").val(),
        "NoiDungThanhToan": $("#input-dntt-noidungthanhtoan").val(),
        "GhiChu": "",
        "ThemChiHoId": themChiHoId.join(','),
        "XoaChiHoId": xoaChiHoId.join(','),
       
    }

    var messageTitle = "Thêm mới đề nghị thanh toán thành công!";
    if (Id != "") {
        messageTitle = "Cập nhật đề nghị thanh toán thành công!";
    }

    $.ajax({
        type: "POST",
        url: "QuanLyChiHo.aspx/InsertUpdateDNTT",
        data: JSON.stringify({ dntt }),
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        async: false,
        success: function (responsive) {
            d = responsive.d;
           
            //console.log(d)
            if (d && d != "-1") {
                var dnttId = d;
                themChiHoId = [];
                xoaChiHoId = [];
                loadMainView();
                swal.fire({
                    title: messageTitle,
                    type: 'success',
                    confirmButtonText: 'OK'
                }).then(function(){
                    var url = "PdfViewer.aspx?FolderName=ChiHo&FileName=DNTT/"+dnttId+"/DNTT" + dnttId + ".pdf";
                    window.open(url, '_blank');
                    $("#modalTaoDNTT").modal("hide");
                });
            }else{
                swal.fire({
                    title: "Thêm mới đề nghị thanh toán thất bại",
                    text: "Báo lại cho admin để xử lý",
                    type: 'error',
                    confirmButtonText: 'OK'
                });
            }



        },
        error: function (responsive) {
            alert("Có lỗi xảy ra! Vui lòng F5(Refresh)!");
        }
    })
    $("#btnSaveDNTT").attr("dnttId", "");
}
// End Hàm thêm mới đề nghị thanh toán
// Start Hàm sao chép vào clipboard
function copyToClipboard(spanId) {
    // Lấy phần tử input
    const inputElement = document.getElementById(spanId).innerText;

    // Sao chép nội dung vào clipboard
    navigator.clipboard.writeText(inputElement).then(() => {
        alert("Đã sao chép: " + inputElement);
    }).catch((err) => {
        console.error("Không thể sao chép", err);
    });
}
// End Hàm sao chép vào clipboard

// Chỉ cho phép nhập số
$('.input-thanhtoan-number').keyup(function (e) {
    FormatCurrency(this);
});
$('.input-thanhtoan-number').keypress(function (e) {
    return CheckNumeric();
});
// End Chỉ cho phép nhập số
// Start Hàm chuyển đổi text quá dài
function fncConvertOverSizeText(text) {
    if (text.length > 20) {
        text = text.substring(0, 10) + "..." + text.substring((text.length - 10), text.length);
    }
    return text;
}
function fncConvertSize(size) {
    var size_float = parseFloat(size);
    var size_return = "";
    if (size_float <= 1000000) {
        size_return = (size_float / 1024).toFixed(2) + " KB";
    } else {
        size_return = (size_float / 1048576).toFixed(2) + " MB";
    }

    return size_return;
}
// End Hàm chuyển đổi text quá dài
// Start Hàm reset thanh trạng thái upload
function fncResetProcessBar() {
    $("#div-upload-process-bar").attr("style", "width:" + 0 + "%");
    $("#div-upload-process-bar").text(0 + "%");
}
// End Hàm reset thanh trạng thái upload
// Start Hàm load file đính kèm
function fncLoadFileDinhKem(chiHoId) {
    //$("#div-wait").show();
    $("#tbl-upload-imgzone tbody").empty();
    //$("#div-filedinhkem-list").append("<tr id=\"tr-filedinhkem-loading\"><td colspan=\"6\"> <img alt=\"\" src=\"images/squares.gif\" id=\"img-checklist-box-loading\"/></td> </tr>");

    ajaxGet = { "get": chiHoId };
    jsonData = JSON.stringify({ ajaxGet });
    $.ajax({
        type: "POST",
        url: "QuanLyChiHo.aspx/reFileDinhKem",
        data: jsonData,
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        async: false,
        success: function (responsive) {
            d = responsive.d;
            var html_filedinhkem = "";
            //console.log(d);
            $.each(d, function (item, val) {
                html_filedinhkem += "<tr class=\"tr-upload-daupload\" filename=\"" + val.filename + "\" folder=\"" + chiHoId + "\">";
                html_filedinhkem += "<td>" + "<span class=\"span-upload-trangthai label label-success\">" + "Đã upload" + "</span>" + "</td>";
                //html_filedinhkem += "<td>" + "<img class=\"img-pre-upload\" src=\"" + "" + "\"  alt=\"Photo\" />" + "</td>";
                html_filedinhkem += "<td>" + val.filename + "</td>";
                html_filedinhkem += "<td>" + fncConvertSize(val.filesize) + "</td>";
                html_filedinhkem += "<td>" + "<a class=\"label label-info\" id=\"a-dinhkem-taixuong\">Tải xuống</a>" +"<a class=\"label label-danger\" id=\"a-dinhkem-xoa\">Xóa</a>" + "</td>";
                html_filedinhkem += "</tr>";
            })

            
                $("#tr-filedinhkem-loading").remove();
                $("#tbl-upload-imgzone tbody").empty();
                $("#tbl-upload-imgzone tbody").append(html_filedinhkem);
                //$("#myModalLabelActivity").append("<span> (Có " + d.length + " file đính kèm)</span>")
            
        },
        error: function (responsive) {
            alert("Có lỗi xảy ra! Vui lòng F5(Refresh)!");
        }
    }).done(function () {
        if ($("#tbl-upload-imgzone tbody tr").length === 0) {
            $("#tbl-upload-imgzone tbody").append("<tr><td colspan=\"4\" class=\"text-center\">Không có file upload</td></tr>");
        }
        //$("#div-wait").hide();
    })
}
// End Hàm load file đính kèm
// Start Hàm xử lý drop file
function handleDrop(e) {
    let dt = e.dataTransfer;
    let files = dt.files;
    //console.log(dt);
    //console.log(files);
    count_item = $("#tbl-upload-imgzone tbody tr.tr-upload-chuaupload").length + $("#tbl-upload-imgzone tbody tr.tr-upload-daupload").length;
    if(count_item == 0){
        $("#tbl-upload-imgzone tbody").empty();
    }
    $.each(files, function (item, val) {
        html_imgzone = "";
        if (val.size < 10000000) {
            var fileExtension = val.name.split('.').pop();
            var awb = $("#input-chiho-awbbill").val();
            var ncc = $("#input-chiho-ncu").val();
            var khachhang = $("#input-chiho-khachhang").val();
            var sohoadon = $("#input-chiho-sohd").val();
            var newFileName = awb + "-" + ncc + "-" + khachhang + "-" + sohoadon + "-" + String(count_item + 1) + "." + fileExtension;
            var newFile = new File([val], newFileName, { type: val.type });
            arrUploadData["file" + count_item] = newFile;
            arrTempData["file" + count_item] = val;
            tmppath = URL.createObjectURL(val);
            html_imgzone += "<tr class=\"tr-upload-chuaupload\">";
            html_imgzone += "<td>" + "<span class=\"span-upload-trangthai label label-default\">" + "Chưa upload" + "</span>" + "</td>";
            //html_imgzone += "<td>" + "<img class=\"img-pre-upload\" src=\"" + tmppath + "\"  alt=\"Photo\" />" + "</td>";
            html_imgzone += "<td>" + newFileName + "</td>";
            html_imgzone += "<td>" + fncConvertSize(val.size) + "</td>";
            html_imgzone += "<td>" + "<a class=\"btn btn-danger btn-sm btn-upload-delete\" fileitem=\"file" + count_item + "\" ><i class=\"glyphicon glyphicon-trash\"></i> Xóa</a>" + "</td>";
            html_imgzone += "</tr>";
            count_item += 1;
        }
    })
    $("#tbl-upload-imgzone").append(html_imgzone);
    //handleFiles(files);
}
// End Hàm xử lý drop file
// Start Hàm tính số tiền trước VAT
function tinhSoTienTruocVAT(soTienSauVAT) {
    if( soTienSauVAT != null && soTienSauVAT != 0){
        const lamTronXuong = Math.floor(soTienSauVAT / 1.08);
        const lamTronLen = Math.ceil(soTienSauVAT / 1.08);

        if (Math.round(lamTronXuong * 1.08) === soTienSauVAT) {
            return lamTronXuong;
        } else if (Math.round(lamTronLen * 1.08) === soTienSauVAT) {
            return lamTronLen;
        } else {
            return 0; 
        }
    }else{
        return 0;
    }   
}
// End Hàm tính số tiền trước VAT
// Start Hàm chuyển hex sang array buffer
function hexToArrayBuffer(hex) {
    var bytes = new Uint8Array(hex.length / 2);
    for (var i = 0; i < hex.length; i += 2) {
        bytes[i / 2] = parseInt(hex.substr(i, 2), 16);
    }
    return bytes.buffer;
}
// End Hàm chuyển hex sang array buffer
// Start Hàm xem file pdf từ hex        
function viewPdfFromHex(hexData) {
    var arrayBuffer = hexToArrayBuffer(hexData);
    var blob = new Blob([arrayBuffer], { type: 'application/pdf' });
    var url = URL.createObjectURL(blob);
    window.open(url, '_blank');
}
// End Hàm xem file pdf từ hex

// Start Hàm tìm người bán
function fncTimNguoiBan(nccSelected) {
    var nguoiBan = "";
    var tempListNCC = listNCC.filter(function(ncc){
            return ncc.NCC == nccSelected;
        });
    if (tempListNCC.length > 0) {
        nguoiBan = tempListNCC[0].TenCty;
    }
    return nguoiBan;
}
// Start Hàm xóa khoảng trắng và ký tự đặc biệt
function fncCleanString(value) {
    return String(value).trim().replace(/ /g, '');
}
// End Hàm xóa khoảng trắng và ký tự đặc biệt

// Start Hàm xóa dấu tiếng Việt
function removeDiacritics(str) {
    return str.normalize("NFD").replace(/[\u0300-\u036f]/g, "");
}
// End Hàm xóa dấu tiếng Việt

// Start Pagination Variables
var paginationData = {
    currentPage: 1,
    itemsPerPage: 50,
    totalItems: 0,
    totalPages: 0,
    allRows: []
};
// End Pagination Variables

// Start Pagination Functions
function initializePagination() {
    // Khởi tạo sự kiện cho các nút pagination
    $("#select-chiho-items-per-page").on("change", function() {
        paginationData.itemsPerPage = parseInt($(this).val());
        paginationData.currentPage = 1;
        updatePagination();
    });

    $("#btn-chiho-prev-page").on("click", function() {
        if (paginationData.currentPage > 1) {
            paginationData.currentPage--;
            updatePagination();
        }
    });

    $("#btn-chiho-next-page").on("click", function() {
        if (paginationData.currentPage < paginationData.totalPages) {
            paginationData.currentPage++;
            updatePagination();
        }
    });
}

function setPaginationData(rows) {
    // Lưu trữ tất cả các rows
    paginationData.allRows = rows;
    paginationData.totalItems = rows.length;
    paginationData.totalPages = Math.ceil(paginationData.totalItems / paginationData.itemsPerPage);
    paginationData.currentPage = 1;

    updatePagination();
}

function updatePagination() {
    // Tính toán các chỉ số
    var startIndex = (paginationData.currentPage - 1) * paginationData.itemsPerPage;
    var endIndex = Math.min(startIndex + paginationData.itemsPerPage, paginationData.totalItems);

    // Ẩn tất cả các rows
    $("#tbl-chiho tbody tr").hide();

    // Hiển thị các rows trong trang hiện tại
    for (var i = startIndex; i < endIndex; i++) {
        if (paginationData.allRows[i]) {
            $(paginationData.allRows[i]).show();
        }
    }

    // Cập nhật thông tin pagination
    updatePaginationInfo();
    updatePaginationButtons();
}

function updatePaginationInfo() {
    var startIndex = (paginationData.currentPage - 1) * paginationData.itemsPerPage + 1;
    var endIndex = Math.min(paginationData.currentPage * paginationData.itemsPerPage, paginationData.totalItems);

    // Cập nhật thông tin trang
    $("#btn-chiho-page-info").text("Trang " + paginationData.currentPage + " / " + paginationData.totalPages);

    // Cập nhật summary
    if (paginationData.totalItems > 0) {
        $("#span-chiho-pagination-summary").text("Hiển thị " + startIndex + " - " + endIndex + " của " + paginationData.totalItems + " mục");
    } else {
        $("#span-chiho-pagination-summary").text("Hiển thị 0 - 0 của 0 mục");
    }
}

function updatePaginationButtons() {
    // Cập nhật trạng thái nút Previous
    if (paginationData.currentPage <= 1) {
        $("#btn-chiho-prev-page").prop("disabled", true);
    } else {
        $("#btn-chiho-prev-page").prop("disabled", false);
    }

    // Cập nhật trạng thái nút Next
    if (paginationData.currentPage >= paginationData.totalPages || paginationData.totalPages === 0) {
        $("#btn-chiho-next-page").prop("disabled", true);
    } else {
        $("#btn-chiho-next-page").prop("disabled", false);
    }
}

function resetPagination() {
    paginationData.currentPage = 1;
    paginationData.totalItems = 0;
    paginationData.totalPages = 0;
    paginationData.allRows = [];

    updatePaginationInfo();
    updatePaginationButtons();
}
// End Pagination Functions